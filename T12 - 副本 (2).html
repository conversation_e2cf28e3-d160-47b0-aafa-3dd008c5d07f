<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小牛电动车销售与维修管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.2.0/dist/chartjs-plugin-datalabels.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        /* 样式优化 - 精简重复代码，提升渲染性能 */
        :root {
            --primary: #3498db;
            --secondary: #2ecc71;
            --danger: #e74c3c;
            --warning: #f39c12;
            --dark: #2c3e50;
            --light: #ecf0f1;
            --premium: #9b59b6;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 6px 15px rgba(0,0,0,0.15);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
            transform: rotate(30deg);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            position: relative;
            z-index: 2;
        }
        
        .logo i {
            font-size: 2.5rem;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            padding: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        
        .logo h1 {
            font-size: 1.8rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .header-controls {
            display: flex;
            gap: 10px;
            position: relative;
            z-index: 2;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: white;
        }
        
        .btn-success {
            background-color: var(--secondary);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger);
            color: white;
        }
        
        .btn-warning {
            background-color: var(--warning);
            color: white;
        }
        
        .btn-premium {
            background-color: var(--premium);
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0,0,0,0.15);
        }
        
        .tabs {
            display: flex;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }
        
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            text-align: center;
            flex: 1;
            border-bottom: 3px solid transparent;
            position: relative;
        }
        
        .tab.active {
            border-bottom: 3px solid var(--primary);
            color: var(--primary);
            background-color: rgba(52, 152, 219, 0.1);
        }
        
        .tab:hover:not(.active) {
            background-color: rgba(52, 152, 219, 0.05);
        }
        
        .tab-content {
            display: none;
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 6px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .tab-content.active {
            display: block;
        }
        
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border: 1px solid #eee;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: var(--dark);
            position: sticky;
            top: 0;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        tr:hover {
            background-color: #f1f7ff;
        }
        
        .action-cell {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.85rem;
            cursor: pointer;
            border: none;
            transition: all 0.2s;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .chart-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid #eee;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark);
        }
        
        .chart-wrapper {
            height: 300px;
            position: relative;
        }
        
        .form-container {
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid #eee;
        }
        
        .form-title {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .form-group {
            flex: 1;
            min-width: 250px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border 0.3s;
        }
        
        .form-group textarea {
            min-height: 80px;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .inventory-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            border: 1px solid #eee;
            transition: transform 0.3s;
        }
        
        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.1);
        }
        
        .summary-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            border-radius: 50%;
        }
        
        .summary-icon i {
            font-size: 1.8rem;
        }
        
        .summary-icon.new {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(46, 204, 113, 0.1));
        }
        
        .summary-icon.used {
            background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(231, 76, 60, 0.1));
        }
        
        .summary-icon.premium {
            background: linear-gradient(135deg, rgba(155, 89, 182, 0.1), rgba(142, 68, 173, 0.1));
        }
        
        .summary-icon.parts {
            background: linear-gradient(135deg, rgba(149, 165, 166, 0.1), rgba(127, 140, 141, 0.1));
        }
        
        .summary-icon.sales {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.1));
        }
        
        .summary-icon.repair {
            background: linear-gradient(135deg, rgba(241, 196, 15, 0.1), rgba(243, 156, 18, 0.1));
        }
        
        .summary-value {
            font-size: 2rem;
            font-weight: 700;
            margin: 5px 0;
            color: var(--dark);
        }
        
        .summary-label {
            color: #777;
            font-size: 0.95rem;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            box-shadow: 0 6px 15px rgba(0,0,0,0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            transform: translateX(120%);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background: linear-gradient(135deg, var(--secondary), #27ae60);
        }
        
        .notification.error {
            background: linear-gradient(135deg, var(--danger), #c0392b);
        }
        
        footer {
            text-align: center;
            padding: 20px;
            margin-top: 40px;
            color: #777;
            font-size: 0.9rem;
            border-top: 1px solid #eee;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #777;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #ddd;
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }
        
        .modal-overlay.active .modal {
            transform: translateY(0);
        }
        
        .modal-header {
            padding: 20px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 1.4rem;
            font-weight: 600;
        }
        
        .close-modal {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .close-modal:hover {
            transform: rotate(90deg);
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .detail-label {
            flex: 0 0 120px;
            font-weight: 600;
            color: #555;
        }
        
        .detail-value {
            flex: 1;
        }
        
        .modal-footer {
            padding: 15px 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            border-top: 1px solid #eee;
        }
        
        @media (max-width: 768px) {
            .tabs {
                flex-wrap: wrap;
            }
            
            .tab {
                flex: 1 0 33%;
                padding: 12px;
                font-size: 0.9rem;
            }
            
            .charts-container {
                grid-template-columns: 1fr;
            }
            
            .chart-wrapper {
                height: 250px;
            }
            
            .header-controls {
                flex-direction: column;
                gap: 8px;
            }
            
            header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }
            
            .detail-row {
                flex-direction: column;
            }
            
            .detail-label {
                margin-bottom: 5px;
            }
        }
        
        .multi-select {
            height: 120px;
        }
        
        .confirmation-dialog {
            text-align: center;
            padding: 20px;
        }
        
        .confirmation-text {
            font-size: 1.1rem;
            margin-bottom: 20px;
        }
        
        .dialog-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .part-item {
            margin-bottom: 10px;
            padding: 8px;
            border: 1px solid #eee;
            border-radius: 5px;
        }
        
        .part-item label {
            margin-left: 8px;
        }
        
        .part-quantity {
            width: 70px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        /* 筛选样式 */
        .filter-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
            align-items: center;
            padding: 12px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #eee;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-group label {
            font-weight: 500;
            min-width: 50px;
        }
        
        .filter-group select, .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .filter-indicator {
            background-color: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .filter-indicator i {
            cursor: pointer;
        }
        
        .days-remaining {
            font-weight: bold;
        }
        .days-remaining.positive {
            color: #2ecc71;
        }
        .days-remaining.negative {
            color: #e74c3c;
        }
        
        .stock-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        .stock-status.out-of-stock {
            background-color: #ffebee;
            color: #e74c3c;
        }
        .stock-status.low {
            background-color: #fff3e0;
            color: #f39c12;
        }
        .stock-status.adequate {
            background-color: #e8f5e9;
            color: #2ecc71;
        }
        
        .parts-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .subtitle {
            font-weight: bold;
            margin: 10px 0 5px;
            color: #3498db;
        }
        
        /* 新增样式：选装项目表格 */
        .accessory-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .accessory-table th, 
        .accessory-table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .accessory-table th {
            background-color: #f8f9fa;
        }
        
        .accessory-table input {
            width: 80px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .add-accessory-btn {
            margin-top: 10px;
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .accessory-total {
            margin-top: 10px;
            font-weight: bold;
            text-align: right;
        }
        
        /* 新车库存已售出样式 */
        .sold-row {
            text-decoration: line-through;
            color: #888;
        }
        
        /* 下拉菜单样式 */
        .accessory-select {
            width: 100%;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-motorcycle"></i>
                <h1>小牛电动车销售与维修管理系统</h1>
            </div>
<div class="header-controls">
    <button class="btn btn-danger" id="clearDataBtn" style="margin-right: 10px;">
        <i class="fas fa-trash-alt"></i> 清除所有数据
    </button>
    <button class="btn btn-primary" id="exportBtn">
        <i class="fas fa-file-export"></i> 导出数据
    </button>
    <button class="btn btn-success" id="importBtn">
        <i class="fas fa-file-import"></i> 导入数据
    </button>
    <input type="file" id="fileInput" accept=".xlsx, .xls" style="display: none;">
</div>
        </header>
        
<div class="tabs">
            <div class="tab active" data-tab="dashboard">管理看板</div>
            <div class="tab" data-tab="orders">订单管理</div>
            <div class="tab" data-tab="deliveries">交车管理</div>
            <div class="tab" data-tab="repairs">维修管理</div>
            <div class="tab" data-tab="inventory">库存管理</div>
            <div class="tab" data-tab="reports" style="display:none;">统计报表</div>
            <div class="tab" data-tab="finance">收款</div>
            <div class="tab" data-tab="financial">财务</div>
            <div class="tab" data-tab="settings">设置</div>
        </div>
        
        <!-- 管理看板 -->
        <div class="tab-content active" id="dashboard">
            <div class="inventory-summary">
                <div class="summary-card">
                    <div class="summary-icon new">
                        <i class="fas fa-bicycle"></i>
                    </div>
                    <div class="summary-value" id="newInventoryCount">0</div>
                    <div class="summary-label">新车库存</div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon used">
                        <i class="fas fa-bicycle"></i>
                    </div>
                    <div class="summary-value" id="usedInventoryCount">0</div>
                    <div class="summary-label">二手车库存</div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon premium">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div class="summary-value" id="premiumInventoryCount">0</div>
                    <div class="summary-label">精品库存</div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon parts">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="summary-value" id="partsInventoryCount">0</div>
                    <div class="summary-label">配件库存</div>
                </div>
                <!-- 新增本月订单统计 -->
                <div class="summary-card">
                    <div class="summary-icon sales">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="summary-value" id="monthlyOrderCount">0</div>
                    <div class="summary-label">本月订单</div>
                </div>
                <!-- 新增本月交车统计 -->
                <div class="summary-card">
                    <div class="summary-icon sales">
                        <i class="fas fa-truck-loading"></i>
                    </div>
                    <div class="summary-value" id="monthlyDeliveryCount">0</div>
                    <div class="summary-label">本月交车</div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon sales">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="summary-value" id="monthlySales">¥0</div>
                    <div class="summary-label">本月销售额</div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon repair">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="summary-value" id="monthlyRepairRevenue">¥0</div>
                    <div class="summary-label">本月维修额</div>
                </div>
            </div>
            
            <div class="charts-container">
                <div class="chart-card">
                    <div class="chart-header">
                        <div class="chart-title">库存车型分布</div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="inventoryBikeModelChart"></canvas>
                    </div>
                </div>
                <div class="chart-card">
                    <div class="chart-header">
                        <div class="chart-title">订单车型分布</div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="bikeModelChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="table-container">
                <h3 style="margin-bottom: 15px;">待处理订单</h3>
                <table id="pendingOrdersTable">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>客户姓名</th>
                            <th>车型</th>
                            <th>数量</th>
                            <th>订单日期</th>
                            <th>交付日期</th>
                            <th>状态</th>
                            <th>剩余天数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 订单管理 -->
        <div class="tab-content" id="orders">
            <div class="form-container">
                <h2 class="form-title"><i class="fas fa-plus-circle"></i> <span id="orderFormTitle">添加新订单</span></h2>
                <input type="hidden" id="editOrderId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="orderId">订单号</label>
                        <input type="text" id="orderId" placeholder="自动生成" disabled>
                    </div>
                    <div class="form-group">
                        <label for="customerName">客户姓名</label>
                        <input type="text" id="customerName" placeholder="请输入客户姓名" required>
                    </div>
                    <div class="form-group">
                        <label for="customerPhone">联系电话</label>
                        <input type="tel" id="customerPhone" placeholder="请输入联系电话" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="bikeModel">车型选择</label>
                        <select id="bikeModel" required>
                            <option value="">请选择车型</option>
                            <!-- 动态填充 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="quantity">数量</label>
                        <input type="number" id="quantity" min="1" value="1" required>
                    </div>
                    <div class="form-group">
                        <label for="color">颜色</label>
                        <input type="text" id="color" placeholder="请输入颜色">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="orderDate">订单日期</label>
                        <input type="date" id="orderDate" required>
                    </div>
                    <div class="form-group">
                        <label for="deliveryDate">交付日期</label>
                        <input type="date" id="deliveryDate">
                    </div>
                    <div class="form-group">
                        <label for="orderStatus">库存状态</label>
<select id="orderStatus" required>
    <option value="">请选择</option>
    <option value="in_stock">现货</option>
    <option value="futures">期货</option>
    <option value="cancelled">取消</option>
</select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
<label for="amount">订单金额 (¥)</label>
                        <input type="number" id="amount" min="0" step="0.01" placeholder="请输入金额" required>
                    </div>
                    <div class="form-group">
                        <label for="deposit">订金 (¥)</label>
                        <input type="number" id="deposit" min="0" step="0.01" placeholder="请输入订金">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="orderNote">备注</label>
                        <textarea id="orderNote" placeholder="请输入订单备注"></textarea>
                    </div>
                </div>
                
                <div class="form-row">
                    <button class="btn btn-success" id="saveOrderBtn" style="margin-top: 15px;">
                        <i class="fas fa-save"></i> 保存订单
                    </button>
                    <button class="btn btn-warning" id="cancelEditBtn" style="margin-top: 15px; display: none;">
                        <i class="fas fa-times"></i> 取消编辑
                    </button>
                </div>
            </div>
            
            <div class="table-container">
                <h3 style="margin-bottom: 15px;">订单列表</h3>
                <table id="ordersTable">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>订单号</th>
                            <th>客户姓名</th>
                            <th>车型</th>
                            <th>数量</th>
                            <th>金额 (¥)</th>
                            <th>订金 (¥)</th>
                            <th>订单日期</th>
                            <th>交付日期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 交车管理 -->
        <div class="tab-content" id="deliveries">
            <div class="form-container">
                <h2 class="form-title"><i class="fas fa-truck-loading"></i> <span id="deliveryFormTitle">添加交车记录</span></h2>
                <input type="hidden" id="editDeliveryId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="deliveryId">交车单号</label>
                        <input type="text" id="deliveryId" placeholder="自动生成" disabled>
                    </div>
                    <div class="form-group">
                        <label for="deliveryOrder">关联订单</label>
                        <select id="deliveryOrder" required>
                            <option value="">请选择订单</option>
                            <!-- 动态填充 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="deliveryDate">交车日期</label>
                        <input type="date" id="deliveryDate" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="vin">车架号 (VIN)</label>
                        <select id="vin" required>
                            <option value="">请选择车架号</option>
                            <!-- 动态填充 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="deliveryPerson">交车人</label>
                        <input type="text" id="deliveryPerson" placeholder="请输入交车人姓名" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
<label for="orderAmount">订单金额 (¥)</label>
                        <input type="text" id="orderAmount" placeholder="关联订单后自动显示" readonly>
                    </div>
                </div>
                
                <!-- 选装配件区域 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>选装配件</label>
                        <table class="accessory-table" id="deliveryPartsTable">
                            <thead>
                                <tr>
                                    <th>项目</th>
                                    <th>数量</th>
                                    <th>单价 (¥)</th>
                                    <th>总价 (¥)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态添加配件行 -->
                            </tbody>
                        </table>
                        <button type="button" class="add-accessory-btn" onclick="addPartRow('deliveryPartsTable')">添加配件</button>
                    </div>
                </div>
                
                <!-- 选装精品区域 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>选装精品</label>
                        <table class="accessory-table" id="deliveryPremiumsTable">
                            <thead>
                                <tr>
                                    <th>项目</th>
                                    <th>数量</th>
                                    <th>单价 (¥)</th>
                                    <th>总价 (¥)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态添加精品行 -->
                            </tbody>
                        </table>
                        <button type="button" class="add-accessory-btn" onclick="addPremiumRow('deliveryPremiumsTable')">添加精品</button>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="accessoryTotal">选装总计 (¥)</label>
                        <input type="text" id="accessoryTotal" value="0.00" readonly>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="deliveryNote">备注</label>
                        <input type="text" id="deliveryNote" placeholder="请输入备注信息">
                    </div>
                </div>
                
                <div class="form-row">
                    <button class="btn btn-success" id="saveDeliveryBtn" style="margin-top: 15px;">
                        <i class="fas fa-check-circle"></i> 保存交车记录
                    </button>
                    <button class="btn btn-warning" id="cancelDeliveryEditBtn" style="margin-top: 15px; display: none;">
                        <i class="fas fa-times"></i> 取消编辑
                    </button>
                </div>
            </div>
            
            <div class="table-container">
                <h3 style="margin-bottom: 15px;">交车记录</h3>
                <table id="deliveriesTable">
<thead>
    <tr>
        <th>序号</th>
        <th>交车单号</th>
        <th>订单号</th>
        <th>客户姓名</th>
        <th>车型</th>
        <th>车架号</th>
        <th>交车日期</th>
        <th>订单金额</th>
        <th>操作</th>
    </tr>
</thead>
                    <tbody>
                        <!-- 动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 维修管理 -->
        <div class="tab-content" id="repairs">
            <div class="form-container">
                <h2 class="form-title"><i class="fas fa-tools"></i> <span id="repairFormTitle">添加维修记录</span></h2>
                <input type="hidden" id="editRepairId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="repairId">维修单号</label>
                        <input type="text" id="repairId" placeholder="自动生成" disabled>
                    </div>
                    <div class="form-group">
                        <label for="repairCustomer">客户姓名</label>
                        <input type="text" id="repairCustomer" placeholder="请输入客户姓名" required>
                    </div>
                    <div class="form-group">
                        <label for="repairPhone">联系电话</label>
                        <input type="tel" id="repairPhone" placeholder="请输入联系电话" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="bikeModelRepair">维修车型</label>
                        <select id="bikeModelRepair" required>
                            <option value="">请选择车型</option>
                            <!-- 动态填充 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="repairDate">维修日期</label>
                        <input type="date" id="repairDate" required>
                    </div>
                    <div class="form-group">
                        <label for="repairStatus">维修状态</label>
                        <select id="repairStatus" required>
                            <option value="diagnosing">检测中</option>
                            <option value="repairing">维修中</option>
                            <option value="waiting">待取件</option>
                            <option value="completed">已完成</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="repairType">维修类型</label>
                        <select id="repairType" required>
                            <option value="">请选择维修类型</option>
                            <option value="电池问题">电池问题</option>
                            <option value="电机故障">电机故障</option>
                            <option value="控制器故障">控制器故障</option>
                            <option value="刹车系统">刹车系统</option>
                            <option value="轮胎更换">轮胎更换</option>
                            <option value="灯光系统">灯光系统</option>
                            <option value="其他问题">其他问题</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="repairFee">维修收费 (¥)</label>
                        <input type="number" id="repairFee" min="0" step="0.01" placeholder="请输入维修收费">
                    </div>
                </div>
                
                <!-- 配件区域 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>使用配件</label>
                        <table class="accessory-table" id="repairPartsTable">
                            <thead>
                                <tr>
                                    <th>项目</th>
                                    <th>数量</th>
                                    <th>单价 (¥)</th>
                                    <th>总价 (¥)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态添加配件行 -->
                            </tbody>
                        </table>
                        <button type="button" class="add-accessory-btn" onclick="addPartRow('repairPartsTable')">添加配件</button>
                    </div>
                </div>
                
                <!-- 精品区域 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>使用精品</label>
                        <table class="accessory-table" id="repairPremiumsTable">
                            <thead>
                                <tr>
                                    <th>项目</th>
                                    <th>数量</th>
                                    <th>单价 (¥)</th>
                                    <th>总价 (¥)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态添加精品行 -->
                            </tbody>
                        </table>
                        <button type="button" class="add-accessory-btn" onclick="addPremiumRow('repairPremiumsTable')">添加精品</button>
                    </div>
                </div>

                <!-- 工时费区域 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>工时费</label>
                        <table class="accessory-table" id="repairLaborTable">
                            <thead>
                                <tr>
                                    <th>项目</th>
                                    <th>数量</th>
                                    <th>单价 (¥)</th>
                                    <th>总价 (¥)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态添加工时费行 -->
                            </tbody>
                        </table>
                        <button type="button" class="add-accessory-btn" onclick="addLaborRow()">添加工时费</button>
                        <div class="accessory-total" style="margin-top: 10px;">
                            附件总计: <span id="laborTotalAmount">¥0.00</span>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="repairNote">问题描述</label>
                        <textarea id="repairNote" placeholder="请输入问题描述" required></textarea>
                    </div>
                </div>
                
                <div class="form-row">
                    <button class="btn btn-success" id="saveRepairBtn" style="margin-top: 15px;">
                        <i class="fas fa-wrench"></i> 保存维修记录
                    </button>
                    <button class="btn btn-warning" id="cancelRepairEditBtn" style="margin-top: 15px; display: none;">
                        <i class="fas fa-times"></i> 取消编辑
                    </button>
                </div>
            </div>
            
            <div class="table-container">
                <h3 style="margin-bottom: 15px;">维修记录</h3>
                <table id="repairsTable">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>维修单号</th>
                            <th>客户</th>
                            <th>车型</th>
                            <th>维修类型</th>
                            <th>配件价格</th>
                            <th>精品价格</th>
                            <th>工时费</th>
                            <th>维修收费</th>
                            <th>维修日期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 库存管理 -->
        <div class="tab-content" id="inventory">
            <div class="tabs" style="margin-bottom: 20px;">
                <div class="tab active" data-subtab="new">新车库存</div>
                <!-- 隐藏二手车库存标签 -->
                <!--<div class="tab" data-subtab="used">二手车库存</div>-->
                <div class="tab" data-subtab="premium">精品库存</div>
                <div class="tab" data-subtab="parts">配件库存</div>
            </div>
            
            <div class="subtab-content active" id="new-inventory">
                <div class="form-container">
                <h2 class="form-title"><i class="fas fa-plus-circle"></i> <span id="newInventoryFormTitle">添加车辆库存</span></h2>
                    <input type="hidden" id="editNewInventoryId">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="newModel">车型</label>
                            <select id="newModel" required>
                                <option value="">请选择车型</option>
                                <!-- 动态填充 -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="newColor">颜色</label>
                            <input type="text" id="newColor" placeholder="请输入颜色" required>
                        </div>
                        <div class="form-group">
                            <label for="newVin">车架号 (VIN)</label>
                            <input type="text" id="newVin" placeholder="请输入车架号" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="newStockDate">入库日期</label>
                            <input type="date" id="newStockDate" required>
                        </div>
                        <div class="form-group">
                            <label for="newCost">成本价 (¥)</label>
                            <input type="number" id="newCost" min="0" step="0.01" placeholder="请输入成本价" required>
                        </div>
                        <div class="form-group">
                            <label for="newPrice">销售价 (¥)</label>
                            <input type="number" id="newPrice" min="0" step="0.01" placeholder="请输入销售价" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="newStatus">状态</label>
                            <select id="newStatus" required>
                                <option value="in_stock">在库</option>
                                <option value="reserved">已预订</option>
                                <!-- 已售出选项已移除 -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="vehicleAttribute">车辆属性</label>
                            <select id="vehicleAttribute" required>
                                <option value="new">新车</option>
                                <option value="used">二手车</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="newNote">备注</label>
                            <textarea id="newNote" placeholder="请输入备注"></textarea>
                        </div>
                    </div>
                    
                    <div class="form-row">
<button class="btn btn-success" id="saveNewInventoryBtn" style="margin-top: 15px;">
    <i class="fas fa-save"></i> 保存车辆信息
</button>
                        <button class="btn btn-warning" id="cancelNewEditBtn" style="margin-top: 15px; display: none;">
                            <i class="fas fa-times"></i> 取消编辑
                        </button>
                    </div>
                </div>
                
                <div class="table-container">
                    <h3 style="margin-bottom: 15px;">车辆库存列表</h3>
                    <table id="newInventoryTable">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>车型</th>
                                <th>颜色</th>
                                <th>车架号</th>
                                <th>入库日期</th>
                                <th>成本价 (¥)</th>
                                <th>销售价 (¥)</th>
                                <th>状态</th>
                                <th>车辆属性</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="subtab-content" id="used-inventory" style="display:none;">
                <div class="form-container">
                    <h2 class="form-title"><i class="fas fa-plus-circle"></i> <span id="usedInventoryFormTitle">添加二手车库存</span></h2>
                    <input type="hidden" id="editUsedInventoryId">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="usedModel">车型</label>
                            <input type="text" id="usedModel" placeholder="请输入车型" required>
                        </div>
                        <div class="form-group">
                            <label for="usedColor">颜色</label>
                            <input type="text" id="usedColor" placeholder="请输入颜色" required>
                        </div>
                        <div class="form-group">
                            <label for="usedVin">车架号 (VIN)</label>
                            <input type="text" id="usedVin" placeholder="请输入车架号" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="usedStockDate">入库日期</label>
                            <input type="date" id="usedStockDate" required>
                        </div>
                        <div class="form-group">
                            <label for="usedMileage">行驶里程 (km)</label>
                            <input type="number" id="usedMileage" min="0" placeholder="请输入里程" required>
                        </div>
                        <div class="form-group">
                            <label for="usedCost">成本价 (¥)</label>
                            <input type="number" id="usedCost" min="0" step="0.01" placeholder="请输入成本价" required>
                        </div>
                        <div class="form-group">
                            <label for="usedPrice">销售价 (¥)</label>
                            <input type="number" id="usedPrice" min="0" step="0.01" placeholder="请输入销售价" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="usedStatus">状态</label>
                            <select id="usedStatus" required>
                                <option value="in_stock">在库</option>
                                <option value="reserved">已预订</option>
                                <option value="sold">已售出</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="usedNote">备注</label>
                            <input type="text" id="usedNote" placeholder="车辆状况备注">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <button class="btn btn-success" id="saveUsedInventoryBtn" style="margin-top: 15px;">
                            <i class="fas fa-save"></i> 保存二手车信息
                        </button>
                        <button class="btn btn-warning" id="cancelUsedEditBtn" style="margin-top: 15px; display: none;">
                            <i class="fas fa-times"></i> 取消编辑
                        </button>
                    </div>
                </div>
                
                <div class="table-container">
                    <h3 style="margin-bottom: 15px;">二手车库存列表</h3>
                    <table id="usedInventoryTable">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>车型</th>
                                <th>颜色</th>
                                <th>车架号</th>
                                <th>里程 (km)</th>
                                <th>成本价 (¥)</th>
                                <th>销售价 (¥)</th>
                                <th>入库日期</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 新增精品库存模块 -->
            <div class="subtab-content" id="premium-inventory">
                <div class="form-container">
                    <h2 class="form-title"><i class="fas fa-plus-circle"></i> <span id="premiumInventoryFormTitle">添加精品库存</span></h2>
                    <input type="hidden" id="editPremiumInventoryId">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="premiumName">精品名称</label>
                            <input type="text" id="premiumName" placeholder="请输入精品名称" required>
                        </div>
                        <div class="form-group">
                            <label for="premiumCategory">分类</label>
                            <select id="premiumCategory" required>
                                <option value="">请选择分类</option>
                                <option value="helmet">头盔</option>
                                <option value="lock">车锁</option>
                                <option value="cover">车罩</option>
                                <option value="bag">车包</option>
                                <option value="accessory">装饰件</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="premiumModel">适用车型</label>
                            <input type="text" id="premiumModel" placeholder="请输入适用车型">
                        </div>
                        <div class="form-group">
                            <label for="premiumQuantity">数量</label>
                            <input type="number" id="premiumQuantity" min="0" value="1" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="premiumCost">成本价 (¥)</label>
                            <input type="number" id="premiumCost" min="0" step="0.01" placeholder="请输入成本价" required>
                        </div>
                        <div class="form-group">
                            <label for="premiumPrice">销售价 (¥)</label>
                            <input type="number" id="premiumPrice" min="0" step="0.01" placeholder="请输入销售价" required>
                        </div>
                        <div class="form-group">
                            <label for="premiumStockDate">入库日期</label>
                            <input type="date" id="premiumStockDate" required>
                        </div>
                        <div class="form-group">
                            <label for="premiumSupplier">供应商</label>
                            <input type="text" id="premiumSupplier" placeholder="请输入供应商" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="premiumNote">备注</label>
                            <textarea id="premiumNote" placeholder="请输入备注"></textarea>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <button class="btn btn-premium" id="savePremiumInventoryBtn" style="margin-top: 15px;">
                            <i class="fas fa-save"></i> 保存精品信息
                        </button>
                        <button class="btn btn-warning" id="cancelPremiumEditBtn" style="margin-top: 15px; display: none;">
                            <i class="fas fa-times"></i> 取消编辑
                        </button>
                    </div>
                </div>
                
                <div class="table-container">
                    <h3 style="margin-bottom: 15px;">精品库存列表</h3>
                    <table id="premiumInventoryTable">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>精品名称</th>
                                <th>分类</th>
                                <th>适用车型</th>
                                <th>数量</th>
                                <th>成本价 (¥)</th>
                                <th>销售价 (¥)</th>
                                <th>供应商</th>
                                <th>库存状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="subtab-content" id="parts-inventory">
                <div class="form-container">
                    <h2 class="form-title"><i class="fas fa-plus-circle"></i> <span id="partInventoryFormTitle">添加配件库存</span></h2>
                    <input type="hidden" id="editPartInventoryId">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="partName">配件名称</label>
                            <input type="text" id="partName" placeholder="请输入配件名称" required>
                        </div>
                        <div class="form-group">
                            <label for="partModel">适用车型</label>
                            <input type="text" id="partModel" placeholder="请输入适用车型" required>
                        </div>
                        <div class="form-group">
                            <label for="partSpec">规格型号</label>
                            <input type="text" id="partSpec" placeholder="请输入规格型号" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="partQuantity">数量</label>
                            <input type="number" id="partQuantity" min="0" value="1" required>
                        </div>
                        <div class="form-group">
                            <label for="partCost">成本价 (¥)</label>
                            <input type="number" id="partCost" min="0" step="0.01" placeholder="请输入成本价" required>
                        </div>
                        <div class="form-group">
                            <label for="partPrice">销售价 (¥)</label>
                            <input type="number" id="partPrice" min="0" step="0.01" placeholder="请输入销售价" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="partStockDate">入库日期</label>
                            <input type="date" id="partStockDate" required>
                        </div>
                        <div class="form-group">
                            <label for="partSupplier">供应商</label>
                            <input type="text" id="partSupplier" placeholder="请输入供应商" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <button class="btn btn-success" id="savePartInventoryBtn" style="margin-top: 15px;">
                            <i class="fas fa-save"></i> 保存配件信息
                        </button>
                        <button class="btn btn-warning" id="cancelPartEditBtn" style="margin-top: 15px; display: none;">
                            <i class="fas fa-times"></i> 取消编辑
                        </button>
                    </div>
                </div>
                
                <div class="table-container">
                    <h3 style="margin-bottom: 15px;">配件库存列表</h3>
                    <table id="partsInventoryTable">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>配件名称</th>
                                <th>适用车型</th>
                                <th>规格型号</th>
                                <th>数量</th>
                                <th>成本价 (¥)</th>
                                <th>销售价 (¥)</th>
                                <th>供应商</th>
                                <th>库存状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- 统计报表 -->
        <div class="tab-content" id="reports">
            <div class="charts-container">
                <div class="chart-card">
                    <div class="chart-header">
                        <div class="chart-title">月度销售统计</div>
                        <div>
                            <select id="salesYearSelect" style="padding: 5px; border-radius: 4px; border: 1px solid #ddd; margin-right: 10px;">
                                <option value="2020">2020年</option>
                                <option value="2021">2021年</option>
                                <option value="2022">2022年</option>
                                <option value="2023" selected>2023年</option>
                                <option value="2024">2024年</option>
                                <option value="2025">2025年</option>
                            </select>
                            <select id="salesChartType" style="padding: 5px; border-radius: 4px; border: 1px solid #ddd;">
                                <option value="bar">柱状图</option>
                                <option value="line">折线图</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="monthlySalesChart"></canvas>
                    </div>
                </div>
                <div class="chart-card">
                    <div class="chart-header">
                        <div class="chart-title">车型销售占比</div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="modelSalesChart"></canvas>
                    </div>
                </div>
                <div class="chart-card">
                    <div class="chart-header">
                        <div class="chart-title">维修收入统计</div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="repairRevenueChart"></canvas>
                    </div>
                </div>
                <div class="chart-card">
                    <div class="chart-header">
                        <div class="chart-title">库存价值分布</div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="inventoryValueChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="table-container" style="margin-top: 30px;">
                <h3 style="margin-bottom: 15px;">销售统计报表</h3>
                <table id="salesReportTable">
                    <thead>
                        <tr>
                            <th>月份</th>
                            <th>订单数量</th>
                            <th>销售数量</th>
                            <th>销售额 (¥)</th>
                            <th>平均单价 (¥)</th>
                            <th>环比增长</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 收款模块 -->
        <div class="tab-content" id="finance">
            <div class="form-container">
                <h2 class="form-title"><i class="fas fa-money-bill-wave"></i> 添加收款记录</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="paymentId">收款单号</label>
                        <input type="text" id="paymentId" placeholder="自动生成" disabled>
                    </div>
                    <div class="form-group">
                        <label for="paymentOrder">关联单据</label>
                        <select id="paymentOrder" required>
                            <option value="">请选择单据</option>
                            <!-- 动态填充 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="paymentDate">收款日期</label>
                        <input type="date" id="paymentDate" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="paymentAmount">收款金额 (¥)</label>
                        <input type="number" id="paymentAmount" min="0" step="0.01" placeholder="请输入金额" required>
                    </div>
                    <div class="form-group">
                        <label for="paymentMethodFinance">收款方式</label>
                        <select id="paymentMethodFinance" required>
                            <option value="wechat">微信</option>
                            <option value="alipay">支付宝</option>
                            <option value="cash">现金</option>
                            <option value="transfer">转账</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="payer">付款人</label>
                        <input type="text" id="payer" placeholder="请输入付款人姓名" required>
                    </div>
                    <div class="form-group">
                        <label for="receiverFinance">收款人</label>
                        <input type="text" id="receiverFinance" placeholder="请输入收款人姓名" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="paymentNote">备注</label>
                        <textarea id="paymentNote" placeholder="请输入备注"></textarea>
                    </div>
                </div>
                
                <button class="btn btn-success" id="savePaymentBtn" style="margin-top: 15px;">
                    <i class="fas fa-save"></i> 保存收款记录
                </button>
            </div>
            
            <div class="table-container">
                <h3 style="margin-bottom: 15px;">收款记录</h3>
                <table id="paymentsTable">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>收款单号</th>
                            <th>关联单据</th>
                            <th>客户姓名</th>
                            <th>金额 (¥)</th>
                            <th>收款方式</th>
                            <th>收款日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 财务模块 -->
        <div class="tab-content" id="financial">
            <div class="table-container">
                <h3>交车毛利统计</h3>
                <table id="deliveryProfitTable" class="table" style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <thead>
                        <tr style="background-color: #3498db; color: white;">
                            <th style="padding: 10px; border: 1px solid #ddd;">月份</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">毛利（元）</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">本月销售额（元）</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">毛利率</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态填充 -->
                    </tbody>
                </table>

                <h3>维修毛利统计</h3>
                <table id="repairProfitTable" class="table" style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <thead>
                        <tr style="background-color: #2ecc71; color: white;">
                            <th style="padding: 10px; border: 1px solid #ddd;">月份</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">毛利（元）</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">本月维修额（元）</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">毛利率</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态填充 -->
                    </tbody>
                </table>

                <h3>库存金额统计</h3>
                <table id="inventoryValueTable" class="table" style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background-color: #9b59b6; color: white;">
                            <th style="padding: 10px; border: 1px solid #ddd;">库存类型</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">库存数量</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">库存总成本 (元)</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">库存总销售价 (元)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 设置模块 -->
        <div class="tab-content" id="settings">
            <div class="tabs" style="margin-bottom: 20px;">
                <div class="tab active" data-subtab="models">车型管理</div>
            </div>
            <div class="subtab-content active" id="models-inventory">
                <div class="form-container">
                    <h2 class="form-title" id="bikeModelFormTitle">添加车型</h2>
                    <input type="hidden" id="editBikeModelId">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="bikeModelName">车型名称</label>
                            <input type="text" id="bikeModelName" placeholder="请输入车型名称" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="bikeModelNote">备注</label>
                            <textarea id="bikeModelNote" placeholder="请输入备注"></textarea>
                        </div>
                    </div>
                    <div class="form-row">
                        <button class="btn btn-success" id="saveBikeModelBtn" style="margin-top: 15px;">
                            <i class="fas fa-save"></i> 保存车型
                        </button>
                        <button class="btn btn-warning" id="cancelBikeModelEditBtn" style="margin-top: 15px; display: none;">
                            <i class="fas fa-times"></i> 取消编辑
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <h3 style="margin-bottom: 15px;">车型列表</h3>
                    <table id="bikeModelsTable">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>车型名称</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        
        <footer>
            <p>小牛电动车销售与维修管理系统 &copy; 2023 | 数据安全存储 | 版本 2.5.1</p>
        </footer>
    </div>
    
    <!-- 详情弹窗 -->
    <div class="modal-overlay" id="detailModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">详情信息</h3>
                <button class="close-modal" id="closeModal">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 动态填充 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="closeModalBtn">关闭</button>
            </div>
        </div>
    </div>
    
    <!-- 确认对话框 -->
    <div class="modal-overlay" id="confirmationModal">
        <div class="modal">
            <div class="modal-body">
                <div class="confirmation-dialog">
                    <div class="confirmation-text" id="confirmationText">确定要删除此项吗？</div>
                    <div class="dialog-buttons">
                        <button class="btn btn-danger" id="confirmDeleteBtn">删除</button>
                        <button class="btn btn-primary" id="cancelDeleteBtn">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="notification" id="notification">
        <i class="fas fa-check-circle"></i>
        <span id="notification-message">操作成功！</span>
    </div>
    
    <script>
        // 数据存储结构
        const dataModel = {
            orders: [],
            deliveries: [],
            repairs: [],
            newInventory: [],
            usedInventory: [],
            premiumInventory: [],
            partsInventory: [],
            payments: [],
            bikeModels: [],
            nextOrderId: 1,
            nextDeliveryId: 1,
            nextRepairId: 1,
            nextNewInventoryId: 1,
            nextUsedInventoryId: 1,
            nextPremiumInventoryId: 1,
            nextPartInventoryId: 1,
            nextPaymentId: 1,
            nextBikeModelId: 1
        };

        // 全局变量
        let appData = {...dataModel};
        let currentEditId = null;
        let deleteCallback = null;
        
        // DOM 加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 尝试从localStorage加载数据
            const savedData = localStorage.getItem('niu-app-data');
            
            if (savedData) {
                try {
                    appData = JSON.parse(savedData);
                    // 确保所有必要属性都存在
                    appData = {...dataModel, ...appData};
                } catch (e) {
                    console.error('解析保存的数据失败，将使用默认数据', e);
                    appData = {...dataModel};
                    initSampleData();
                }
            } else {
                // 如果没有保存的数据，初始化默认数据
                appData = {...dataModel};
                initSampleData();
            }
            
            // 设置日期字段
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('orderDate').value = today;
            document.getElementById('deliveryDate').value = today;
            document.getElementById('repairDate').value = today;
            document.getElementById('newStockDate').value = today;
            document.getElementById('usedStockDate').value = today;
            document.getElementById('premiumStockDate').value = today;
            document.getElementById('partStockDate').value = today;
            document.getElementById('paymentDate').value = today;
            
            // 生成初始ID（使用新格式）
            document.getElementById('orderId').value = generateOrderId();
            document.getElementById('deliveryId').value = generateDeliveryId();
            document.getElementById('repairId').value = generateRepairId();
            document.getElementById('paymentId').value = generatePaymentId();
            
            // 添加事件监听器
            document.getElementById('saveOrderBtn').addEventListener('click', saveOrder);
            document.getElementById('saveDeliveryBtn').addEventListener('click', saveDelivery);
            document.getElementById('saveRepairBtn').addEventListener('click', saveRepair);
            document.getElementById('saveNewInventoryBtn').addEventListener('click', saveNewInventory);
            document.getElementById('saveUsedInventoryBtn').addEventListener('click', saveUsedInventory);
            document.getElementById('savePremiumInventoryBtn').addEventListener('click', savePremiumInventory);
            document.getElementById('savePartInventoryBtn').addEventListener('click', savePartInventory);
            document.getElementById('savePaymentBtn').addEventListener('click', savePayment);
            document.getElementById('saveBikeModelBtn').addEventListener('click', saveBikeModel);
            // document.getElementById('saveSettingsBtn').addEventListener('click', saveSettings);
            
            document.getElementById('exportBtn').addEventListener('click', exportData);
            document.getElementById('importBtn').addEventListener('click', () => document.getElementById('fileInput').click());
            document.getElementById('fileInput').addEventListener('change', importData);
            document.getElementById('clearDataBtn').addEventListener('click', clearAllData);
            
            document.getElementById('closeModal').addEventListener('click', closeModal);
            document.getElementById('closeModalBtn').addEventListener('click', closeModal);
            
            document.getElementById('confirmDeleteBtn').addEventListener('click', confirmDelete);
            document.getElementById('cancelDeleteBtn').addEventListener('click', closeConfirmation);
            
            document.getElementById('cancelEditBtn').addEventListener('click', cancelEdit);
            document.getElementById('cancelNewEditBtn').addEventListener('click', cancelNewEdit);
            document.getElementById('cancelUsedEditBtn').addEventListener('click', cancelUsedEdit);
            document.getElementById('cancelPremiumEditBtn').addEventListener('click', cancelPremiumEdit);
            document.getElementById('cancelPartEditBtn').addEventListener('click', cancelPartEdit);
            document.getElementById('cancelBikeModelEditBtn').addEventListener('click', cancelBikeModelEdit);
            document.getElementById('cancelDeliveryEditBtn').addEventListener('click', cancelDeliveryEdit);
            document.getElementById('cancelRepairEditBtn').addEventListener('click', cancelRepairEdit);
            
            // 关联订单变更事件
            document.getElementById('deliveryOrder').addEventListener('change', function() {
                const orderId = this.value;
                const order = appData.orders.find(o => o.id === orderId);
                if (order) {
                    document.getElementById('orderAmount').value = parseFloat(order.amount).toFixed(2);
                    // 更新可用的车架号列表
                    updateVinSelect(order.bikeModel);
                } else {
                    document.getElementById('orderAmount').value = '';
                    document.getElementById('vin').innerHTML = '<option value="">请选择车架号</option>';
                }
            });
            
            // 初始化数据
            loadData();
            renderAllTables();
            initCharts();
            
            // 更新库存摘要
            updateInventorySummary();
            
            // 添加年份选择器事件监听
            document.getElementById('salesYearSelect').addEventListener('change', updateCharts);
            document.getElementById('salesChartType').addEventListener('change', updateCharts);
            
            // 添加事件委托处理表格操作按钮
            setupTableEventDelegation();
        });
        
        // 清除所有数据函数
        function clearAllData() {
            if (confirm('确定要清除所有数据吗？此操作不可撤销！')) {
                appData = {...dataModel};
                saveData();
                loadData();
                renderAllTables();
                updateInventorySummary();
                showNotification('所有数据已清除', 'success');
            }
        }

            // 设置事件委托处理表格操作按钮
            function setupTableEventDelegation() {
                // 订单表
                document.querySelector('#ordersTable tbody').addEventListener('click', function(e) {
                    const btn = e.target.closest('.action-btn');
                    if (!btn) return;
                    
                    const row = btn.closest('tr');
                    const id = row.dataset.id;
                    
                    if (btn.classList.contains('edit-btn')) {
                        editOrder(id);
                    } else if (btn.classList.contains('view-btn')) {
                        viewOrder(id);
                    } else if (btn.classList.contains('delete-btn')) {
                        deleteItem('order', id);
                    }
                });
                
                // 交车记录表
                document.querySelector('#deliveriesTable tbody').addEventListener('click', function(e) {
                    const btn = e.target.closest('.action-btn');
                    if (!btn) return;
                    
                    const row = btn.closest('tr');
                    const id = row.dataset.id;
                    
                    if (btn.classList.contains('edit-btn')) {
                        editDelivery(id);
                    } else if (btn.classList.contains('view-btn')) {
                        viewDelivery(id);
                    } else if (btn.classList.contains('delete-btn')) {
                        deleteItem('delivery', id);
                    }
                });
                
                // 维修记录表
                document.querySelector('#repairsTable tbody').addEventListener('click', function(e) {
                    const btn = e.target.closest('.action-btn');
                    if (!btn) return;
                    
                    const row = btn.closest('tr');
                    const id = row.dataset.id;
                    
                    if (btn.classList.contains('edit-btn')) {
                        editRepair(id);
                    } else if (btn.classList.contains('view-btn')) {
                        viewRepair(id);
                    } else if (btn.classList.contains('delete-btn')) {
                        deleteItem('repair', id);
                    }
                });
                
                // 新车库存表
                document.querySelector('#newInventoryTable tbody').addEventListener('click', function(e) {
                    const btn = e.target.closest('.action-btn');
                    if (!btn) return;
                    
                    const row = btn.closest('tr');
                    const id = row.dataset.id;
                    
                    if (btn.classList.contains('edit-btn')) {
                        editNewInventory(id);
                    } else if (btn.classList.contains('delete-btn')) {
                        deleteItem('newInventory', id);
                    }
                });

                // 二手车库存表
                document.querySelector('#usedInventoryTable tbody').addEventListener('click', function(e) {
                    const btn = e.target.closest('.action-btn');
                    if (!btn) return;

                    const row = btn.closest('tr');
                    const id = row.dataset.id;

                    if (btn.classList.contains('edit-btn')) {
                        editUsedInventory(id);
                    } else if (btn.classList.contains('delete-btn')) {
                        deleteItem('usedInventory', id);
                    }
                });

                // 精品库存表
                document.querySelector('#premiumInventoryTable tbody').addEventListener('click', function(e) {
                    const btn = e.target.closest('.action-btn');
                    if (!btn) return;

                    const row = btn.closest('tr');
                    const id = row.dataset.id;

                    if (btn.classList.contains('edit-btn')) {
                        editPremiumInventory(id);
                    } else if (btn.classList.contains('delete-btn')) {
                        deleteItem('premiumInventory', id);
                    }
                });

                // 配件库存表
                document.querySelector('#partsInventoryTable tbody').addEventListener('click', function(e) {
                    const btn = e.target.closest('.action-btn');
                    if (!btn) return;

                    const row = btn.closest('tr');
                    const id = row.dataset.id;

                    if (btn.classList.contains('edit-btn')) {
                        editPartInventory(id);
                    } else if (btn.classList.contains('delete-btn')) {
                        deleteItem('partsInventory', id);
                    }
                });

                // 收款记录表
                document.querySelector('#paymentsTable tbody').addEventListener('click', function(e) {
                    const btn = e.target.closest('.action-btn');
                    if (!btn) return;

                    const row = btn.closest('tr');
                    const id = row.dataset.id;

                    if (btn.classList.contains('view-btn')) {
                        viewPayment(id);
                    } else if (btn.classList.contains('delete-btn')) {
                        deleteItem('payment', id);
                    }
                });

                // 车型列表表
                document.querySelector('#bikeModelsTable tbody').addEventListener('click', function(e) {
                    const btn = e.target.closest('.action-btn');
                    if (!btn) return;

                    const row = btn.closest('tr');
                    const id = row.dataset.id;

                    if (btn.classList.contains('edit-btn')) {
                        editBikeModel(id);
                    } else if (btn.classList.contains('delete-btn')) {
                        deleteItem('bikeModel', id);
                    }
                });
            }

        // 生成订单号（新格式：DD-年份-序号）
        function generateOrderId() {
            const year = new Date().getFullYear().toString().slice(2);
            return `DD-${year}-${appData.nextOrderId.toString().padStart(4, '0')}`;
        }

        // 生成交车单号（新格式：JC-年份-序号）
        function generateDeliveryId() {
            const year = new Date().getFullYear().toString().slice(2);
            return `JC-${year}-${appData.nextDeliveryId.toString().padStart(4, '0')}`;
        }

        // 生成维修单号（新格式：WX-年份-序号）
        function generateRepairId() {
            const year = new Date().getFullYear().toString().slice(2);
            return `WX-${year}-${appData.nextRepairId.toString().padStart(4, '0')}`;
        }
        
        // 生成收款单号（新格式：SK-年份-序号）
        function generatePaymentId() {
            const year = new Date().getFullYear().toString().slice(2);
            return `SK-${year}-${appData.nextPaymentId.toString().padStart(4, '0')}`;
        }

        // 选项卡切换功能
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // 主选项卡切换
                if (!tab.dataset.subtab) {
                    const tabName = tab.dataset.tab;
                    
                    // 移除所有活动标签
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));
                    
                    // 添加活动类
                    tab.classList.add('active');
                    document.getElementById(tabName).classList.add('active');
                    
                    // 如果是报表标签，更新图表
                    if (tabName === 'reports') {
                        updateCharts();
                    }
                    
                    // 如果是维修管理标签，加载配件数据
                    if (tabName === 'repairs') {
                        loadPartsData();
                    }
                    
                    // 如果是设置模块，加载车型数据
                    if (tabName === 'settings') {
                        loadBikeModels();
                    }
                }
                // 库存子选项卡切换
                else {
                    const subtabName = tab.dataset.subtab;
                    const parent = tab.parentElement;
                    
                    // 移除所有活动子标签
                    parent.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.subtab-content').forEach(stc => stc.classList.remove('active'));
                    
                    // 添加活动类
                    tab.classList.add('active');
                    const targetContent = document.getElementById(`${subtabName}-inventory`);
                    if (targetContent) {
                        targetContent.classList.add('active');
                        // 滚动到对应内容区顶部
                        targetContent.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            });
        });

        // 加载数据到表单
        function loadData() {
            // 更新订单下拉菜单
            const deliveryOrderSelect = document.getElementById('deliveryOrder');
            deliveryOrderSelect.innerHTML = '<option value="">请选择订单</option>';
            
            appData.orders.forEach(order => {
                if (order.status === 'in_stock' || order.status === 'futures') {
                    const option = document.createElement('option');
                    option.value = order.id;
                    option.textContent = `${order.id} (${order.customerName})`;
                    deliveryOrderSelect.appendChild(option);
                }
            });
            
            // 更新订单车型下拉菜单，改为引用所有车型名称，不过滤category
            const bikeModelSelect = document.getElementById('bikeModel');
            bikeModelSelect.innerHTML = '<option value="">请选择车型</option>';
            appData.bikeModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = model.name;
                bikeModelSelect.appendChild(option);
            });
            
            // 更新维修车型下拉菜单
            const bikeModelRepairSelect = document.getElementById('bikeModelRepair');
            bikeModelRepairSelect.innerHTML = '<option value="">请选择车型</option>';
            appData.bikeModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = model.name;
                bikeModelRepairSelect.appendChild(option);
            });
            
            // 更新新车库存车型下拉菜单，改为引用所有车型名称
            const newModelSelect = document.getElementById('newModel');
            newModelSelect.innerHTML = '<option value="">请选择车型</option>';
            appData.bikeModels.forEach(model => {
                // 只添加车型名称，不限制分类
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = model.name;
                newModelSelect.appendChild(option);
            });
            
            // 更新收款单据下拉菜单
            const paymentOrderSelect = document.getElementById('paymentOrder');
            paymentOrderSelect.innerHTML = '<option value="">请选择单据</option>';
            
            // 添加订单
            appData.orders.forEach(order => {
                const option = document.createElement('option');
                option.value = `order_${order.id}`;
                option.textContent = `订单: ${order.id} (${order.customerName})`;
                paymentOrderSelect.appendChild(option);
            });
            
            // 添加维修单
            appData.repairs.forEach(repair => {
                const option = document.createElement('option');
                option.value = `repair_${repair.id}`;
                option.textContent = `维修单: ${repair.id} (${repair.customerName})`;
                paymentOrderSelect.appendChild(option);
            });
            
            // 加载配件和精品数据
            loadPartsDataForRepair();
        }
        
        // 更新车架号下拉菜单
function updateVinSelect(model) {
    const vinSelect = document.getElementById('vin');
    vinSelect.innerHTML = '<option value="">请选择车架号</option>';
    
    appData.newInventory.forEach(item => {
        if (item.status === 'in_stock' && item.model === model) {
            const option = document.createElement('option');
            option.value = item.vin;
            option.textContent = `${item.vin} (${item.color})`;
            vinSelect.appendChild(option);
        }
    });
}
        
        // 加载配件和精品数据
        function loadPartsDataForRepair() {
            // 此函数现在不再需要，因为配件和精品数据已经改为表格形式
        }
        
        // 加载车型数据
        function loadBikeModels() {
            const bikeModelsTable = document.querySelector('#bikeModelsTable tbody');
            bikeModelsTable.innerHTML = '';
            
            if (appData.bikeModels.length === 0) {
                bikeModelsTable.innerHTML = `<tr><td colspan="6" class="empty-state"><i class="fas fa-bicycle"></i><p>暂无车型数据</p></td></tr>`;
                return;
            }
            
            appData.bikeModels.forEach((model, index) => {
                const row = document.createElement('tr');
                row.dataset.id = model.id;
                
                let categoryText = '';
                switch(model.category) {
                    case 'new': categoryText = '新车'; break;
                    case 'used': categoryText = '二手车'; break;
                    case 'parts': categoryText = '配件'; break;
                }
                
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${model.name}</td>
                    <td class="action-cell">
                        <button class="action-btn edit-btn" style="background-color: #3498db; color: white;">编辑</button>
                        <button class="action-btn delete-btn" style="background-color: #e74c3c; color: white;">删除</button>
                    </td>
                `;
                bikeModelsTable.appendChild(row);
            });
        }

        // 渲染所有表格
        function renderAllTables() {
            renderOrdersTable();
            renderDeliveriesTable();
            renderRepairsTable();
            renderNewInventoryTable();
            renderUsedInventoryTable();
            renderPremiumInventoryTable();
            renderPartsInventoryTable();
            renderPendingOrdersTable();
            renderSalesReportTable();
            renderPaymentsTable();
            loadBikeModels();
        }

        // 渲染订单表格
        function renderOrdersTable() {
            const tbody = document.querySelector('#ordersTable tbody');
            tbody.innerHTML = '';
            
            if (appData.orders.length === 0) {
                tbody.innerHTML = `<tr><td colspan="11" class="empty-state"><i class="fas fa-clipboard-list"></i><p>暂无订单数据</p></td></tr>`;
                return;
            }
            
            const fragment = document.createDocumentFragment();
            
            appData.orders.forEach((order, index) => {
                const row = document.createElement('tr');
                row.dataset.id = order.id;
                
                // 状态显示
                let statusText, statusColor;
                switch(order.status) {
                    case 'in_stock': 
                        statusText = '现货';
                        statusColor = '#2ecc71';
                        break;
                    case 'futures': 
                        statusText = '期货';
                        statusColor = '#3498db';
                        break;
                    case 'delivery': 
                        statusText = '交货';
                        statusColor = '#9b59b6';
                        break;
                    case 'cancelled': 
                        statusText = '取消';
                        statusColor = '#95a5a6';
                        break;
                }
                
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${order.id}</td>
                    <td>${order.customerName}</td>
                    <td>${order.bikeModel}</td>
                    <td>${order.quantity}</td>
                    <td>¥${parseFloat(order.amount).toFixed(2)}</td>
                    <td>¥${order.deposit ? parseFloat(order.deposit).toFixed(2) : '0.00'}</td>
                    <td>${order.orderDate}</td>
                    <td>${order.deliveryDate || '-'}</td>
                    <td><span style="color: ${statusColor};">${statusText}</span></td>
                    <td class="action-cell">
                        <button class="action-btn edit-btn" style="background-color: #3498db; color: white;">编辑</button>
                        <button class="action-btn view-btn" style="background-color: #2ecc71; color: white;">详情</button>
                        <button class="action-btn delete-btn" style="background-color: #e74c3c; color: white;">删除</button>
                    </td>
                `;
                
                fragment.appendChild(row);
            });
            
            tbody.appendChild(fragment);
        }

        // 编辑订单
        function editOrder(id) {
            const order = appData.orders.find(o => o.id === id);
            if (!order) return;
            
            // 填充表单
            document.getElementById('orderId').value = order.id;
            document.getElementById('customerName').value = order.customerName;
            document.getElementById('customerPhone').value = order.customerPhone;
            document.getElementById('bikeModel').value = order.bikeModel;
            document.getElementById('quantity').value = order.quantity;
            document.getElementById('color').value = order.color || '';
            document.getElementById('orderDate').value = order.orderDate;
            document.getElementById('deliveryDate').value = order.deliveryDate || '';
            document.getElementById('orderStatus').value = order.status;
            document.getElementById('amount').value = order.amount;
            document.getElementById('deposit').value = order.deposit || '';
            document.getElementById('orderNote').value = order.note || '';
            
            // 设置编辑状态
            document.getElementById('editOrderId').value = id;
            document.getElementById('orderFormTitle').textContent = '编辑订单';
            document.getElementById('cancelEditBtn').style.display = 'inline-block';
            
            // 滚动到表单
            document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
        }
        
        // 查看订单详情
        function viewOrder(id) {
            const order = appData.orders.find(o => o.id === id);
            if (!order) return;
            
            document.getElementById('modalTitle').textContent = '订单详情';
            const modalBody = document.getElementById('modalBody');
            
            // 状态显示
            let statusText, statusColor;
            switch(order.status) {
                case 'in_stock': 
                    statusText = '现货';
                    statusColor = '#2ecc71';
                    break;
                case 'futures': 
                    statusText = '期货';
                    statusColor = '#3498db';
                    break;
                case 'delivery': 
                    statusText = '交货';
                    statusColor = '#9b59b6';
                    break;
                case 'cancelled': 
                    statusText = '取消';
                    statusColor = '#95a5a6';
                    break;
            }
            
            modalBody.innerHTML = `
                <div class="detail-row">
                    <div class="detail-label">订单号：</div>
                    <div class="detail-value">${order.id}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">客户姓名：</div>
                    <div class="detail-value">${order.customerName}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">联系电话：</div>
                    <div class="detail-value">${order.customerPhone}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">车型：</div>
                    <div class="detail-value">${order.bikeModel}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">数量：</div>
                    <div class="detail-value">${order.quantity}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">颜色：</div>
                    <div class="detail-value">${order.color || '-'}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">订单日期：</div>
                    <div class="detail-value">${order.orderDate}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">交付日期：</div>
                    <div class="detail-value">${order.deliveryDate || '-'}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">状态：</div>
                    <div class="detail-value"><span style="color: ${statusColor};">${statusText}</span></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">订单总额：</div>
                    <div class="detail-value">¥${parseFloat(order.amount).toFixed(2)}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">订金：</div>
                    <div class="detail-value">¥${order.deposit ? parseFloat(order.deposit).toFixed(2) : '0.00'}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">备注：</div>
                    <div class="detail-value">${order.note || '无'}</div>
                </div>
            `;
            
            openModal();
        }
        
        // 取消编辑
        function cancelEdit() {
            resetOrderForm();
            document.getElementById('cancelEditBtn').style.display = 'none';
        }
        
        // 重置订单表单
        function resetOrderForm() {
            document.getElementById('editOrderId').value = '';
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';
            document.getElementById('bikeModel').value = '';
            document.getElementById('quantity').value = '1';
            document.getElementById('color').value = '';
            document.getElementById('orderStatus').value = 'in_stock';
            document.getElementById('amount').value = '';
            document.getElementById('deposit').value = '';
            document.getElementById('deliveryDate').value = '';
            document.getElementById('orderNote').value = '';
            
            // 生成新订单ID（使用新格式）
            document.getElementById('orderId').value = generateOrderId();
            
            document.getElementById('orderFormTitle').textContent = '添加新订单';
        }

        // 渲染交车表格
        function renderDeliveriesTable() {
            const tbody = document.querySelector('#deliveriesTable tbody');
            tbody.innerHTML = '';
            
            if (appData.deliveries.length === 0) {
                tbody.innerHTML = `<tr><td colspan="8" class="empty-state"><i class="fas fa-truck-loading"></i><p>暂无交车记录</p></td></tr>`;
                return;
            }
            
            const fragment = document.createDocumentFragment();
            
appData.deliveries.forEach((delivery, index) => {
    const row = document.createElement('tr');
    row.dataset.id = delivery.id;
    
    // 查找对应订单金额
    const order = appData.orders.find(o => o.id === delivery.orderId);
    const orderAmount = order ? `¥${parseFloat(order.amount).toFixed(2)}` : '未知';
    
    row.innerHTML = `
        <td>${index + 1}</td>
        <td>${delivery.id}</td>
        <td>${delivery.orderId}</td>
        <td>${delivery.customerName}</td>
        <td>${delivery.bikeModel}</td>
        <td>${delivery.vin}</td>
        <td>${delivery.deliveryDate}</td>
        <td>${orderAmount}</td>
                <td class="action-cell">
                    <button class="action-btn view-btn" style="background-color: #2ecc71; color: white;">查看</button>
                    <button class="action-btn delete-btn" style="background-color: #e74c3c; color: white;">删除</button>
                </td>
    `;
    fragment.appendChild(row);
});
            
            tbody.appendChild(fragment);
        }

// 编辑交车记录
function editDelivery(id) {
    const delivery = appData.deliveries.find(d => d.id === id);
    if (!delivery) return;
    
    // 填充表单
    document.getElementById('deliveryId').value = delivery.id;
    document.getElementById('deliveryOrder').value = delivery.orderId;
    document.getElementById('deliveryDate').value = delivery.deliveryDate;
    document.getElementById('vin').value = delivery.vin;
    document.getElementById('deliveryPerson').value = delivery.deliveryPerson;
    document.getElementById('deliveryNote').value = delivery.note || '';
    
    // 填充选装配件表格
    const partsTableBody = document.getElementById('deliveryPartsTable').querySelector('tbody');
    partsTableBody.innerHTML = '';
    if (delivery.accessoryData && delivery.accessoryData.parts) {
        delivery.accessoryData.parts.forEach(part => {
            addPartRow('deliveryPartsTable', part.id, part.quantity);
        });
    }
    
    // 填充选装精品表格
    const premiumsTableBody = document.getElementById('deliveryPremiumsTable').querySelector('tbody');
    premiumsTableBody.innerHTML = '';
    if (delivery.accessoryData && delivery.accessoryData.premiums) {
        delivery.accessoryData.premiums.forEach(premium => {
            addPremiumRow('deliveryPremiumsTable', premium.id, premium.quantity);
        });
    }
    
    // 设置编辑状态
    document.getElementById('editDeliveryId').value = id;
    document.getElementById('deliveryFormTitle').textContent = '编辑交车记录';
    document.getElementById('cancelDeliveryEditBtn').style.display = 'inline-block';
    
    // 滚动到表单
    document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
}
        
// 查看交车记录
function viewDelivery(id) {
    const delivery = appData.deliveries.find(d => d.id === id);
    if (!delivery) return;
    
    const order = appData.orders.find(o => o.id === delivery.orderId);
    
    document.getElementById('modalTitle').textContent = '交车记录详情';
    const modalBody = document.getElementById('modalBody');
    let partsHtml = '<p>无</p>';
    if (delivery.accessoryData && delivery.accessoryData.parts && delivery.accessoryData.parts.length > 0) {
        partsHtml = '<ul>';
        delivery.accessoryData.parts.forEach(part => {
            partsHtml += `<li>${part.name} x${part.quantity}，单价¥${part.price.toFixed(2)}，总价¥${part.total.toFixed(2)}</li>`;
        });
        partsHtml += '</ul>';
    }
    let premiumsHtml = '<p>无</p>';
    if (delivery.accessoryData && delivery.accessoryData.premiums && delivery.accessoryData.premiums.length > 0) {
        premiumsHtml = '<ul>';
        delivery.accessoryData.premiums.forEach(premium => {
            premiumsHtml += `<li>${premium.name} x${premium.quantity}，单价¥${premium.price.toFixed(2)}，总价¥${premium.total.toFixed(2)}</li>`;
        });
        premiumsHtml += '</ul>';
    }
    modalBody.innerHTML = `
        <div class="detail-row">
            <div class="detail-label">交车单号：</div>
            <div class="detail-value">${delivery.id}</div>
        </div>
        <div class="detail-row">
            <div class="detail-label">订单号：</div>
            <div class="detail-value">${delivery.orderId}</div>
        </div>
        <div class="detail-row">
            <div class="detail-label">客户姓名：</div>
            <div class="detail-value">${delivery.customerName}</div>
        </div>
        <div class="detail-row">
            <div class="detail-label">车型：</div>
            <div class="detail-value">${delivery.bikeModel}</div>
        </div>
        <div class="detail-row">
            <div class="detail-label">车架号 (VIN)：</div>
            <div class="detail-value">${delivery.vin}</div>
        </div>
        <div class="detail-row">
            <div class="detail-label">交车日期：</div>
            <div class="detail-value">${delivery.deliveryDate}</div>
        </div>
        <div class="detail-row">
            <div class="detail-label">订单金额：</div>
            <div class="detail-value">¥${order ? parseFloat(order.amount).toFixed(2) : '未知'}</div>
        </div>
        <div class="detail-row">
            <div class="detail-label">交车人：</div>
            <div class="detail-value">${delivery.deliveryPerson}</div>
        </div>
        <div class="detail-row">
            <div class="detail-label">选装配件：</div>
            <div class="detail-value">${partsHtml}</div>
        </div>
        <div class="detail-row">
            <div class="detail-label">选装精品：</div>
            <div class="detail-value">${premiumsHtml}</div>
        </div>
        <div class="detail-row">
            <div class="detail-label">备注：</div>
            <div class="detail-value">${delivery.note || '无'}</div>
        </div>
    `;
    
    openModal();
}
        
        // 取消交车编辑
        function cancelDeliveryEdit() {
            resetDeliveryForm();
            document.getElementById('cancelDeliveryEditBtn').style.display = 'none';
        }
        
        // 重置交车表单
        function resetDeliveryForm() {
            document.getElementById('editDeliveryId').value = '';
            document.getElementById('deliveryOrder').value = '';
            document.getElementById('vin').value = '';
            document.getElementById('deliveryPerson').value = '';
            document.getElementById('deliveryNote').value = '';
            resetAccessoryTables();
            
            // 生成新交车单ID（使用新格式）
            document.getElementById('deliveryId').value = generateDeliveryId();
            
            document.getElementById('deliveryFormTitle').textContent = '添加交车记录';
        }

        // 渲染维修表格
        function renderRepairsTable() {
            const tbody = document.querySelector('#repairsTable tbody');
            tbody.innerHTML = '';
            
            if (appData.repairs.length === 0) {
                tbody.innerHTML = `<tr><td colspan="12" class="empty-state"><i class="fas fa-tools"></i><p>暂无维修记录</p></td></tr>`;
                return;
            }
            
            const fragment = document.createDocumentFragment();
            
            appData.repairs.forEach((repair, index) => {
                const row = document.createElement('tr');
                row.dataset.id = repair.id;
                
                // 状态显示
                let statusText, statusColor;
                switch(repair.status) {
                    case 'diagnosing': 
                        statusText = '检测中';
                        statusColor = '#3498db';
                        break;
                    case 'repairing': 
                        statusText = '维修中';
                        statusColor = '#f39c12';
                        break;
                    case 'waiting': 
                        statusText = '待取件';
                        statusColor = '#9b59b6';
                        break;
                    case 'completed': 
                        statusText = '已完成';
                        statusColor = '#2ecc71';
                        break;
                }
                
                // 计算配件价格总和
                let partsPrice = 0;
                if (repair.partsUsed && repair.partsUsed.length > 0) {
                    repair.partsUsed.forEach(item => {
                        const part = appData.partsInventory.find(p => p.id == item.id);
                        if (part) {
                            partsPrice += part.price * item.quantity;
                        }
                    });
                }
                
                // 计算精品价格总和
                let premiumsPrice = 0;
                if (repair.premiumsUsed && repair.premiumsUsed.length > 0) {
                    repair.premiumsUsed.forEach(item => {
                        const premium = appData.premiumInventory.find(p => p.id == item.id);
                        if (premium) {
                            premiumsPrice += premium.price * item.quantity;
                        }
                    });
                }
                
                // 工时费
                let laborFee = repair.laborFee ? parseFloat(repair.laborFee) : 0;
                
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${repair.id}</td>
                    <td>${repair.customerName}</td>
                    <td>${repair.bikeModel}</td>
                    <td>${repair.repairType}</td>
                    <td>¥${partsPrice.toFixed(2)}</td>
                    <td>¥${premiumsPrice.toFixed(2)}</td>
                    <td>¥${laborFee.toFixed(2)}</td>
                    <td>¥${parseFloat(repair.repairFee || 0).toFixed(2)}</td>
                    <td>${repair.repairDate}</td>
                    <td><span style="color: ${statusColor};">${statusText}</span></td>
                    <td class="action-cell">
                        <button class="action-btn edit-btn" style="background-color: #3498db; color: white;">编辑</button>
                        <button class="action-btn view-btn" style="background-color: #2ecc71; color: white;">详情</button>
                        <button class="action-btn delete-btn" style="background-color: #e74c3c; color: white;">删除</button>
                    </td>
                `;
                fragment.appendChild(row);
            });
            
            tbody.appendChild(fragment);
        }

        // 编辑维修记录
        function editRepair(id) {
            const repair = appData.repairs.find(r => r.id === id);
            if (!repair) return;
            
            // 填充表单
            document.getElementById('repairId').value = repair.id;
            document.getElementById('repairCustomer').value = repair.customerName;
            document.getElementById('repairPhone').value = repair.customerPhone;
            document.getElementById('bikeModelRepair').value = repair.bikeModel;
            document.getElementById('repairDate').value = repair.repairDate;
            document.getElementById('repairStatus').value = repair.status;
            document.getElementById('repairType').value = repair.repairType;
            document.getElementById('repairFee').value = repair.repairFee || '';
            document.getElementById('repairNote').value = repair.note || '';
            
            // 填充配件和精品表格
            resetRepairPartsTables();
            
            // 填充配件
            if (repair.partsUsed && repair.partsUsed.length > 0) {
                repair.partsUsed.forEach(item => {
                    addPartRow('repairPartsTable', item.id, item.quantity);
                });
            }
            
            // 填充精品
            if (repair.premiumsUsed && repair.premiumsUsed.length > 0) {
                repair.premiumsUsed.forEach(item => {
                    addPremiumRow('repairPremiumsTable', item.id, item.quantity);
                });
            }
            
            // 设置编辑状态
            document.getElementById('editRepairId').value = id;
            document.getElementById('repairFormTitle').textContent = '编辑维修记录';
            document.getElementById('cancelRepairEditBtn').style.display = 'inline-block';
            
            // 滚动到表单
            document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
        }
        
        // 查看维修记录
        function viewRepair(id) {
            const repair = appData.repairs.find(r => r.id === id);
            if (!repair) return;
            
            // 获取配件名称
            let partsUsedText = '无';
            if (repair.partsUsed && repair.partsUsed.length > 0) {
                const parts = repair.partsUsed.map(item => {
                    const part = appData.partsInventory.find(p => p.id == item.id);
                    return part ? `${part.name} (${part.spec}) x${item.quantity}` : '';
                }).filter(name => name);
                partsUsedText = parts.join(', ');
            }
            
            // 获取精品名称
            let premiumsUsedText = '无';
            if (repair.premiumsUsed && repair.premiumsUsed.length > 0) {
                const premiums = repair.premiumsUsed.map(item => {
                    const premium = appData.premiumInventory.find(p => p.id == item.id);
                    return premium ? `${premium.name} x${item.quantity}` : '';
                }).filter(name => name);
                premiumsUsedText = premiums.join(', ');
            }

            // 获取工时费
            let laborFeeText = '无';
            if (repair.laborFee && repair.laborFee > 0) {
                laborFeeText = `¥${parseFloat(repair.laborFee).toFixed(2)}`;
            } else {
                laborFeeText = '¥0.00';
            }
            
            document.getElementById('modalTitle').textContent = '维修记录详情';
            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <div class="detail-row">
                    <div class="detail-label">维修单号：</div>
                    <div class="detail-value">${repair.id}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">客户姓名：</div>
                    <div class="detail-value">${repair.customerName}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">联系电话：</div>
                    <div class="detail-value">${repair.customerPhone}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">维修车型：</div>
                    <div class="detail-value">${repair.bikeModel}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">维修日期：</div>
                    <div class="detail-value">${repair.repairDate}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">维修类型：</div>
                    <div class="detail-value">${repair.repairType}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">使用配件：</div>
                    <div class="detail-value">${partsUsedText}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">使用精品：</div>
                    <div class="detail-value">${premiumsUsedText}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">工时费：</div>
                    <div class="detail-value">${laborFeeText}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">维修收费：</div>
                    <div class="detail-value">¥${parseFloat(repair.repairFee || 0).toFixed(2)}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">维修状态：</div>
                    <div class="detail-value">${getRepairStatusText(repair.status)}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">问题描述：</div>
                    <div class="detail-value">${repair.note || '无'}</div>
                </div>
            `;
            
            openModal();
        }
        
        function getRepairStatusText(status) {
            switch(status) {
                case 'diagnosing': return '检测中';
                case 'repairing': return '维修中';
                case 'waiting': return '待取件';
                case 'completed': return '已完成';
                default: return status;
            }
        }
        
        // 取消维修编辑
        function cancelRepairEdit() {
            resetRepairForm();
            document.getElementById('cancelRepairEditBtn').style.display = 'none';
        }
        
        // 重置维修表单
        function resetRepairForm() {
            document.getElementById('editRepairId').value = '';
            document.getElementById('repairCustomer').value = '';
            document.getElementById('repairPhone').value = '';
            document.getElementById('bikeModelRepair').value = '';
            document.getElementById('repairType').value = '';
            document.getElementById('repairFee').value = '';
            document.getElementById('repairNote').value = '';
            resetRepairPartsTables();
            
            // 生成新维修单ID（使用新格式）
            document.getElementById('repairId').value = generateRepairId();
            
            document.getElementById('repairFormTitle').textContent = '添加维修记录';
        }
        
        // 重置维修配件和精品表格
        function resetRepairPartsTables() {
            document.getElementById('repairPartsTable').querySelector('tbody').innerHTML = '';
            document.getElementById('repairPremiumsTable').querySelector('tbody').innerHTML = '';
        }

        // 渲染新车库存表格
        function renderNewInventoryTable() {
            const tbody = document.querySelector('#newInventoryTable tbody');
            tbody.innerHTML = '';
            
            if (appData.newInventory.length === 0) {
                tbody.innerHTML = `<tr><td colspan="10" class="empty-state"><i class="fas fa-bicycle"></i><p>暂无车辆库存</p></td></tr>`;
                return;
            }
            
            const fragment = document.createDocumentFragment();
            
            appData.newInventory.forEach((item, index) => {
                const row = document.createElement('tr');
                row.dataset.id = item.id;
                
                // 状态显示
                let statusText, statusColor;
                switch(item.status) {
                    case 'in_stock': 
                        statusText = '在库';
                        statusColor = '#2ecc71';
                        break;
                    case 'reserved': 
                        statusText = '已预订';
                        statusColor = '#f39c12';
                        break;
                    case 'sold': 
                        statusText = '已售出';
                        statusColor = '#e74c3c';
                        break;
                }
                
                // 添加已售出样式
                const soldClass = item.status === 'sold' ? 'sold-row' : '';
                
                row.innerHTML = `
                    <td class="${soldClass}">${index + 1}</td>
                    <td class="${soldClass}">${item.model}</td>
                    <td class="${soldClass}">${item.color}</td>
                    <td class="${soldClass}">${item.vin}</td>
                    <td class="${soldClass}">${item.stockDate}</td>
                    <td class="${soldClass}">¥${parseFloat(item.cost).toFixed(2)}</td>
                    <td class="${soldClass}">¥${parseFloat(item.price).toFixed(2)}</td>
                    <td><span style="color: ${statusColor};">${statusText}</span></td>
                    <td>${item.vehicleAttribute === 'new' ? '新车' : item.vehicleAttribute === 'used' ? '二手车' : '-'}</td>
                    <td class="action-cell">
                        <button class="action-btn edit-btn" style="background-color: #3498db; color: white;">编辑</button>
                        <button class="action-btn delete-btn" style="background-color: #e74c3c; color: white;">删除</button>
                    </td>
                `;
                fragment.appendChild(row);
            });
            
            tbody.appendChild(fragment);
        }

        // 编辑新车库存
        function editNewInventory(id) {
            const item = appData.newInventory.find(i => i.id == id);
            if (!item) return;
            
            // 填充表单
            document.getElementById('newModel').value = item.model;
            document.getElementById('newColor').value = item.color;
            document.getElementById('newVin').value = item.vin;
            document.getElementById('newStockDate').value = item.stockDate;
            document.getElementById('newCost').value = item.cost;
            document.getElementById('newPrice').value = item.price;
            document.getElementById('newStatus').value = item.status;
            document.getElementById('newNote').value = item.note || '';
            
            // 设置编辑状态
            document.getElementById('editNewInventoryId').value = id;
            document.getElementById('newInventoryFormTitle').textContent = '编辑新车库存';
            document.getElementById('cancelNewEditBtn').style.display = 'inline-block';
            
            // 滚动到表单
            document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
        }
        
        // 取消新车编辑
        function cancelNewEdit() {
            resetNewInventoryForm();
            document.getElementById('cancelNewEditBtn').style.display = 'none';
        }
        
        // 重置新车表单
        function resetNewInventoryForm() {
            document.getElementById('editNewInventoryId').value = '';
            document.getElementById('newModel').value = '';
            document.getElementById('newColor').value = '';
            document.getElementById('newVin').value = '';
            document.getElementById('newCost').value = '';
            document.getElementById('newPrice').value = '';
            document.getElementById('newNote').value = '';
            
            document.getElementById('newInventoryFormTitle').textContent = '添加新车库存';
        }

        // 渲染二手车库存表格
        function renderUsedInventoryTable() {
            const tbody = document.querySelector('#usedInventoryTable tbody');
            tbody.innerHTML = '';
            
            if (appData.usedInventory.length === 0) {
                tbody.innerHTML = `<tr><td colspan="10" class="empty-state"><i class="fas fa-bicycle"></i><p>暂无二手车库存</p></td></tr>`;
                return;
            }
            
            const fragment = document.createDocumentFragment();
            
            appData.usedInventory.forEach((item, index) => {
                const row = document.createElement('tr');
                row.dataset.id = item.id;
                
                // 状态显示
                let statusText, statusColor;
                switch(item.status) {
                    case 'in_stock': 
                        statusText = '在库';
                        statusColor = '#2ecc71';
                        break;
                    case 'reserved': 
                        statusText = '已预订';
                        statusColor = '#f39c12';
                        break;
                    case 'sold': 
                        statusText = '已售出';
                        statusColor = '#e74c3c';
                        break;
                }
                
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${item.model}</td>
                    <td>${item.color}</td>
                    <td>${item.vin}</td>
                    <td>${item.mileage}</td>
                    <td>¥${parseFloat(item.cost).toFixed(2)}</td>
                    <td>¥${parseFloat(item.price).toFixed(2)}</td>
                    <td>${item.stockDate}</td>
                    <td><span style="color: ${statusColor};">${statusText}</span></td>
                    <td class="action-cell">
                        <button class="action-btn edit-btn" style="background-color: #3498db; color: white;">编辑</button>
                        <button class="action-btn delete-btn" style="background-color: #e74c3c; color: white;">删除</button>
                    </td>
                `;
                fragment.appendChild(row);
            });
            
            tbody.appendChild(fragment);
        }

        // 编辑二手车库存
        function editUsedInventory(id) {
            const item = appData.usedInventory.find(i => i.id == id);
            if (!item) return;
            
            // 填充表单
            document.getElementById('usedModel').value = item.model;
            document.getElementById('usedColor').value = item.color;
            document.getElementById('usedVin').value = item.vin;
            document.getElementById('usedStockDate').value = item.stockDate;
            document.getElementById('usedMileage').value = item.mileage;
            document.getElementById('usedCost').value = item.cost;
            document.getElementById('usedPrice').value = item.price;
            document.getElementById('usedStatus').value = item.status;
            document.getElementById('usedNote').value = item.note || '';
            
            // 设置编辑状态
            document.getElementById('editUsedInventoryId').value = id;
            document.getElementById('usedInventoryFormTitle').textContent = '编辑二手车库存';
            document.getElementById('cancelUsedEditBtn').style.display = 'inline-block';
            
            // 滚动到表单
            document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
        }
        
        // 取消二手车编辑
        function cancelUsedEdit() {
            resetUsedInventoryForm();
            document.getElementById('cancelUsedEditBtn').style.display = 'none';
        }
        
        // 重置二手车表单
        function resetUsedInventoryForm() {
            document.getElementById('editUsedInventoryId').value = '';
            document.getElementById('usedModel').value = '';
            document.getElementById('usedColor').value = '';
            document.getElementById('usedVin').value = '';
            document.getElementById('usedMileage').value = '';
            document.getElementById('usedCost').value = '';
            document.getElementById('usedPrice').value = '';
            document.getElementById('usedNote').value = '';
            
            document.getElementById('usedInventoryFormTitle').textContent = '添加二手车库存';
        }
        
        // 渲染精品库存表格
        function renderPremiumInventoryTable() {
            const tbody = document.querySelector('#premiumInventoryTable tbody');
            tbody.innerHTML = '';
            
            if (appData.premiumInventory.length === 0) {
                tbody.innerHTML = `<tr><td colspan="10" class="empty-state"><i class="fas fa-gift"></i><p>暂无精品库存</p></td></tr>`;
                return;
            }
            
            const fragment = document.createDocumentFragment();
            
            appData.premiumInventory.forEach((item, index) => {
                const row = document.createElement('tr');
                row.dataset.id = item.id;
                
                // 库存状态
                let stockStatusClass, stockStatusText;
                if (item.quantity === 0) {
                    stockStatusClass = "out-of-stock";
                    stockStatusText = "缺货";
                } else if (item.quantity <= 5) {
                    stockStatusClass = "low";
                    stockStatusText = "紧张";
                } else {
                    stockStatusClass = "adequate";
                    stockStatusText = "充足";
                }
                
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${item.name}</td>
                    <td>${getPremiumCategoryText(item.category)}</td>
                    <td>${item.model || '-'}</td>
                    <td>${item.quantity}</td>
                    <td>¥${parseFloat(item.cost).toFixed(2)}</td>
                    <td>¥${parseFloat(item.price).toFixed(2)}</td>
                    <td>${item.supplier}</td>
                    <td><span class="stock-status ${stockStatusClass}">${stockStatusText}</span></td>
                    <td class="action-cell">
                        <button class="action-btn edit-btn" style="background-color: #3498db; color: white;">编辑</button>
                        <button class="action-btn delete-btn" style="background-color: #e74c3c; color: white;">删除</button>
                    </td>
                `;
                fragment.appendChild(row);
            });
            
            tbody.appendChild(fragment);
        }
        
        // 获取精品分类文本
        function getPremiumCategoryText(category) {
            switch(category) {
                case 'helmet': return '头盔';
                case 'lock': return '车锁';
                case 'cover': return '车罩';
                case 'bag': return '车包';
                case 'accessory': return '装饰件';
                case 'other': return '其他';
                default: return category;
            }
        }
        
        // 编辑精品库存
        function editPremiumInventory(id) {
            const item = appData.premiumInventory.find(i => i.id == id);
            if (!item) return;
            
            // 填充表单
            document.getElementById('premiumName').value = item.name;
            document.getElementById('premiumCategory').value = item.category;
            document.getElementById('premiumModel').value = item.model || '';
            document.getElementById('premiumQuantity').value = item.quantity;
            document.getElementById('premiumCost').value = item.cost;
            document.getElementById('premiumPrice').value = item.price;
            document.getElementById('premiumStockDate').value = item.stockDate;
            document.getElementById('premiumSupplier').value = item.supplier;
            document.getElementById('premiumNote').value = item.note || '';
            
            // 设置编辑状态
            document.getElementById('editPremiumInventoryId').value = id;
            document.getElementById('premiumInventoryFormTitle').textContent = '编辑精品库存';
            document.getElementById('cancelPremiumEditBtn').style.display = 'inline-block';
            
            // 滚动到表单
            document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
        }
        
        // 取消精品编辑
        function cancelPremiumEdit() {
            resetPremiumInventoryForm();
            document.getElementById('cancelPremiumEditBtn').style.display = 'none';
        }
        
        // 重置精品表单
        function resetPremiumInventoryForm() {
            document.getElementById('editPremiumInventoryId').value = '';
            document.getElementById('premiumName').value = '';
            document.getElementById('premiumCategory').value = '';
            document.getElementById('premiumModel').value = '';
            document.getElementById('premiumQuantity').value = '1';
            document.getElementById('premiumCost').value = '';
            document.getElementById('premiumPrice').value = '';
            document.getElementById('premiumSupplier').value = '';
            document.getElementById('premiumNote').value = '';
            
            document.getElementById('premiumInventoryFormTitle').textContent = '添加精品库存';
        }

        // 渲染配件库存表格
        function renderPartsInventoryTable() {
            const tbody = document.querySelector('#partsInventoryTable tbody');
            tbody.innerHTML = '';
            
            if (appData.partsInventory.length === 0) {
                tbody.innerHTML = `<tr><td colspan="10" class="empty-state"><i class="fas fa-cog"></i><p>暂无配件库存</p></td></tr>`;
                return;
            }
            
            const fragment = document.createDocumentFragment();
            
            appData.partsInventory.forEach((item, index) => {
                const row = document.createElement('tr');
                row.dataset.id = item.id;
                
                // 库存状态
                let stockStatusClass, stockStatusText;
                if (item.quantity === 0) {
                    stockStatusClass = "out-of-stock";
                    stockStatusText = "缺货";
                } else if (item.quantity <= 5) {
                    stockStatusClass = "low";
                    stockStatusText = "紧张";
                } else {
                    stockStatusClass = "adequate";
                    stockStatusText = "充足";
                }
                
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${item.name}</td>
                    <td>${item.model}</td>
                    <td>${item.spec}</td>
                    <td>${item.quantity}</td>
                    <td>¥${parseFloat(item.cost).toFixed(2)}</td>
                    <td>¥${parseFloat(item.price).toFixed(2)}</td>
                    <td>${item.supplier}</td>
                    <td><span class="stock-status ${stockStatusClass}">${stockStatusText}</span></td>
                    <td class="action-cell">
                        <button class="action-btn edit-btn" style="background-color: #3498db; color: white;">编辑</button>
                        <button class="action-btn delete-btn" style="background-color: #e74c3c; color: white;">删除</button>
                    </td>
                `;
                fragment.appendChild(row);
            });
            
            tbody.appendChild(fragment);
        }

        // 编辑配件库存
        function editPartInventory(id) {
            const item = appData.partsInventory.find(i => i.id == id);
            if (!item) return;
            
            // 填充表单
            document.getElementById('partName').value = item.name;
            document.getElementById('partModel').value = item.model;
            document.getElementById('partSpec').value = item.spec;
            document.getElementById('partQuantity').value = item.quantity;
            document.getElementById('partCost').value = item.cost;
            document.getElementById('partPrice').value = item.price;
            document.getElementById('partStockDate').value = item.stockDate;
            document.getElementById('partSupplier').value = item.supplier;
            
            // 设置编辑状态
            document.getElementById('editPartInventoryId').value = id;
            document.getElementById('partInventoryFormTitle').textContent = '编辑配件库存';
            document.getElementById('cancelPartEditBtn').style.display = 'inline-block';
            
            // 滚动到表单
            document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
        }
        
        // 取消配件编辑
        function cancelPartEdit() {
            resetPartInventoryForm();
            document.getElementById('cancelPartEditBtn').style.display = 'none';
        }
        
        // 重置配件表单
        function resetPartInventoryForm() {
            document.getElementById('editPartInventoryId').value = '';
            document.getElementById('partName').value = '';
            document.getElementById('partModel').value = '';
            document.getElementById('partSpec').value = '';
            document.getElementById('partQuantity').value = '1';
            document.getElementById('partCost').value = '';
            document.getElementById('partPrice').value = '';
            document.getElementById('partSupplier').value = '';
            
            document.getElementById('partInventoryFormTitle').textContent = '添加配件库存';
        }
        
        // 渲染收款记录表格
        function renderPaymentsTable() {
            const tbody = document.querySelector('#paymentsTable tbody');
            tbody.innerHTML = '';
            
            if (appData.payments.length === 0) {
                tbody.innerHTML = `<tr><td colspan="8" class="empty-state"><i class="fas fa-money-bill-wave"></i><p>暂无收款记录</p></td></tr>`;
                return;
            }
            
            const fragment = document.createDocumentFragment();
            
            appData.payments.forEach((payment, index) => {
                const row = document.createElement('tr');
                row.dataset.id = payment.id;
                
                // 收款方式显示
                let paymentMethodText = '';
                switch(payment.paymentMethod) {
                    case 'wechat': paymentMethodText = '微信'; break;
                    case 'alipay': paymentMethodText = '支付宝'; break;
                    case 'cash': paymentMethodText = '现金'; break;
                    case 'transfer': paymentMethodText = '转账'; break;
                }
                
                // 获取关联单据信息
                let docType = '';
                let customerName = '';
                let docId = '';
                
                if (payment.orderId.startsWith('order_')) {
                    docType = '订单';
                    const orderId = payment.orderId.replace('order_', '');
                    const order = appData.orders.find(o => o.id === orderId);
                    if (order) {
                        customerName = order.customerName;
                        docId = orderId;
                    }
                } else if (payment.orderId.startsWith('repair_')) {
                    docType = '维修单';
                    const repairId = payment.orderId.replace('repair_', '');
                    const repair = appData.repairs.find(r => r.id === repairId);
                    if (repair) {
                        customerName = repair.customerName;
                        docId = repairId;
                    }
                }
                
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${payment.id}</td>
                    <td>${docType}: ${docId}</td>
                    <td>${customerName || '未知'}</td>
                    <td>¥${parseFloat(payment.amount).toFixed(2)}</td>
                    <td>${paymentMethodText}</td>
                    <td>${payment.paymentDate}</td>
                    <td class="action-cell">
                        <button class="action-btn edit-btn" style="background-color: #3498db; color: white;" onclick="editPayment('${payment.id}')">编辑</button>
                        <button class="action-btn view-btn" style="background-color: #2ecc71; color: white;" onclick="viewPayment('${payment.id}')">详情</button>
                        <button class="action-btn delete-btn" style="background-color: #e74c3c; color: white;">删除</button>
                    </td>
                `;
                fragment.appendChild(row);
            });
            
            tbody.appendChild(fragment);
        }
        
        // 查看收款记录
        function viewPayment(id) {
            const payment = appData.payments.find(p => p.id === id);
            if (!payment) return;
            
            // 收款方式显示
            let paymentMethodText = '';
            switch(payment.paymentMethod) {
                case 'wechat': paymentMethodText = '微信'; break;
                case 'alipay': paymentMethodText = '支付宝'; break;
                case 'cash': paymentMethodText = '现金'; break;
                case 'transfer': paymentMethodText = '转账'; break;
            }
            
            // 获取关联单据信息
            let docType = '';
            let docId = '';
            let customerName = '';
            let amount = '';
            let date = '';
            
            if (payment.orderId.startsWith('order_')) {
                docType = '订单';
                const orderId = payment.orderId.replace('order_', '');
                const order = appData.orders.find(o => o.id === orderId);
                if (order) {
                    docId = orderId;
                    customerName = order.customerName;
                    amount = order.amount;
                    date = order.orderDate;
                }
            } else if (payment.orderId.startsWith('repair_')) {
                docType = '维修单';
                const repairId = payment.orderId.replace('repair_', '');
                const repair = appData.repairs.find(r => r.id === repairId);
                if (repair) {
                    docId = repairId;
                    customerName = repair.customerName;
                    amount = repair.repairFee;
                    date = repair.repairDate;
                }
            }
            
            document.getElementById('modalTitle').textContent = '收款记录详情';
            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <div class="detail-row">
                    <div class="detail-label">收款单号：</div>
                    <div class="detail-value">${payment.id}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">关联单据：</div>
                    <div class="detail-value">${docType}: ${docId}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">客户姓名：</div>
                    <div class="detail-value">${customerName || '未知'}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">收款金额：</div>
                    <div class="detail-value">¥${parseFloat(payment.amount).toFixed(2)}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">收款方式：</div>
                    <div class="detail-value">${paymentMethodText}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">收款日期：</div>
                    <极值>
                    <div class="detail-value">${payment.paymentDate}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">付款人：</div>
                    <div class="detail-value">${payment.payer}</div>
                </div>
                    <div class="detail-row">
                        <div class="detail-label">收款人：</div>
                        <div class="detail-value">${payment.receiver}</div>
                    </div>
                <div class="detail-row">
                    <div class="detail-label">备注：</div>
                    <div class="detail-value">${payment.note || '无'}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">关联金额：</div>
                    <div class="detail-value">¥${amount ? parseFloat(amount).toFixed(2) : '未知'}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">关联日期：</div>
                    <div class="detail-value">${date || '未知'}</div>
                </div>
            `;
            
            openModal();
        }
        
        // 编辑车型
        function editBikeModel(id) {
            const model = appData.bikeModels.find(m => m.id == id);
            if (!model) return;
            
            // 填充表单
            document.getElementById('bikeModelName').value = model.name;
            // Removed bikeModelCategory and bikeModelPrice as they are deleted
            document.getElementById('bikeModelNote').value = model.note || '';
            
            // 设置编辑状态
            document.getElementById('editBikeModelId').value = id;
            document.getElementById('bikeModelFormTitle').textContent = '编辑车型';
            document.getElementById('cancelBikeModelEditBtn').style.display = 'inline-block';
            
            // 滚动到表单
            document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
        }
        
        // 取消车型编辑
        function cancelBikeModelEdit() {
            resetBikeModelForm();
            document.getElementById('cancelBikeModelEditBtn').style.display = 'none';
        }
        
        // 重置车型表单
        function resetBikeModelForm() {
            document.getElementById('editBikeModelId').value = '';
            document.getElementById('bikeModelName').value = '';
            // Removed bikeModelCategory and bikeModelPrice as they are deleted
            document.getElementById('bikeModelNote').value = '';
            
            document.getElementById('bikeModelFormTitle').textContent = '添加车型';
        }

        // 保存订单
        function saveOrder() {
            const editId = document.getElementById('editOrderId').value;
            const orderId = document.getElementById('orderId').value;
            const customerName = document.getElementById('customerName').value;
            const customerPhone = document.getElementById('customerPhone').value;
            const bikeModel = document.getElementById('bikeModel').value;
            const quantity = document.getElementById('quantity').value;
            const color = document.getElementById('color').value;
            const orderDate = document.getElementById('orderDate').value;
            const deliveryDate = document.getElementById('deliveryDate').value;
            const orderStatus = document.getElementById('orderStatus').value;
            const amount = document.getElementById('amount').value;
            const deposit = document.getElementById('deposit').value || 0;
            const note = document.getElementById('orderNote').value;
            
            // 简单验证
            if (!customerName || !customerPhone || !bikeModel || !quantity || !orderDate || !orderStatus || !amount) {
                showNotification('请填写必填字段', 'error');
                return;
            }
            
            if (editId) {
                // 更新现有订单
                const orderIndex = appData.orders.findIndex(o => o.id === editId);
                if (orderIndex !== -1) {
                    appData.orders[orderIndex] = {
                        ...appData.orders[orderIndex],
                        customerName,
                        customerPhone,
                        bikeModel,
                        quantity: parseInt(quantity),
                        color,
                        orderDate,
                        deliveryDate,
                        status: orderStatus,
                        amount: parseFloat(amount),
                        deposit: parseFloat(deposit),
                        note
                    };
                    showNotification('订单更新成功！', 'success');
                }
            } else {
                // 创建新订单
                const newOrder = {
                    id: orderId,
                    customerName,
                    customerPhone,
                    bikeModel,
                    quantity: parseInt(quantity),
                    color,
                    orderDate,
                    deliveryDate,
                    status: orderStatus,
                    amount: parseFloat(amount),
                    deposit: parseFloat(deposit),
                    note
                };
                
                // 添加到数据
                appData.orders.push(newOrder);
                appData.nextOrderId++;
                
                // 生成新订单ID（使用新格式）
                document.getElementById('orderId').value = generateOrderId();
                
                showNotification('订单保存成功！', 'success');
            }
            
            // 更新本地存储
            saveData();
            
            // 重新渲染表格
            renderOrdersTable();
            renderPendingOrdersTable();
            loadData(); // 更新下拉菜单
            
            // 重置表单
            resetOrderForm();
        }

        // 保存交车记录
        function saveDelivery() {
            const editId = document.getElementById('editDeliveryId').value;
            const deliveryId = document.getElementById('deliveryId').value;
            const orderId = document.getElementById('deliveryOrder').value;
            const deliveryDate = document.getElementById('deliveryDate').value;
            const vin = document.getElementById('vin').value;
            const deliveryPerson = document.getElementById('deliveryPerson').value;
            const note = document.getElementById('deliveryNote').value;
            
            // 简单验证
            if (!orderId || !vin || !deliveryPerson) {
                showNotification('请填写必填字段', 'error');
                return;
            }
            
            // 查找订单信息
            const order = appData.orders.find(o => o.id === orderId);
            if (!order) {
                showNotification('未找到相关订单', 'error');
                return;
            }
            
            // 计算该订单的收款总金额
            const totalPayment = appData.payments
                .filter(payment => payment.orderId === `order_${orderId}`)
                .reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
            
            // 如果收款总金额小于订单金额，则阻止保存
            if (totalPayment < order.amount) {
                showNotification('该订单的收款金额不足，无法保存交车记录！', 'error');
                return;
            }
            
            // 获取选装配件和精品数据
            const accessoryData = {
                parts: getAccessoryData('deliveryPartsTable'),
                premiums: getAccessoryData('deliveryPremiumsTable')
            };
            
            if (editId) {
                // 更新现有交车记录
                const deliveryIndex = appData.deliveries.findIndex(d => d.id === deliveryId);
                if (deliveryIndex !== -1) {
                    appData.deliveries[deliveryIndex] = {
                        ...appData.deliveries[deliveryIndex],
                        orderId,
                        customerName: order.customerName,
                        bikeModel: order.bikeModel,
                        deliveryDate,
                        vin,
                        deliveryPerson,
                        accessoryData,
                        note
                    };
                    showNotification('交车记录更新成功！', 'success');
                }
            } else {
                // 创建新交车记录
                const newDelivery = {
                    id: deliveryId,
                    orderId,
                    customerName: order.customerName,
                    bikeModel: order.bikeModel,
                    deliveryDate,
                    vin,
                    deliveryPerson,
                    accessoryData,
                    note
                };
                
                // 添加到数据
                appData.deliveries.push(newDelivery);
                appData.nextDeliveryId++;
                
                // 更新订单状态
                order.status = 'delivery';
                
                // 从新车库存中移除该车架号的车辆
                const inventoryItemIndex = appData.newInventory.findIndex(item => item.vin === vin);
                if (inventoryItemIndex !== -1) {
                    appData.newInventory[inventoryItemIndex].status = 'sold';
                }
                
                showNotification('交车记录保存成功！', 'success');
            }
            
            // 更新本地存储
            saveData();
            
            // 重新渲染表格
            renderDeliveriesTable();
            renderOrdersTable();
            renderNewInventoryTable();
            renderPendingOrdersTable();
            loadData(); // 更新下拉菜单
            
            // 重置表单
            resetDeliveryForm();
        }
        
        // 获取选装数据
        function getAccessoryData(tableId) {
            const table = document.getElementById(tableId);
            const rows = table.querySelectorAll('tbody tr');
            const data = [];
            
            rows.forEach(row => {
                const select = row.cells[0].querySelector('select');
                const name = select.options[select.selectedIndex].text;
                const quantity = parseInt(row.cells[1].querySelector('input').value);
                const price = parseFloat(row.cells[2].querySelector('input').value);
                const total = parseFloat(row.cells[3].querySelector('input').value);
                
                if (name && !isNaN(quantity) && !isNaN(price) && !isNaN(total)) {
                    data.push({
                        name,
                        quantity,
                        price,
                        total
                    });
                }
            });
            
            return data;
        }
        
        // 重置选装表格
        function resetAccessoryTables() {
            const partsTable = document.getElementById('deliveryPartsTable');
            const premiumsTable = document.getElementById('deliveryPremiumsTable');
            
            partsTable.querySelector('tbody').innerHTML = '';
            premiumsTable.querySelector('tbody').innerHTML = '';
            document.getElementById('accessoryTotal').value = '0.00';
        }

        // 保存维修记录
        function saveRepair() {
            const editId = document.getElementById('editRepairId').value;
            const repairId = document.getElementById('repairId').value;
            const customerName = document.getElementById('repairCustomer').value;
            const customerPhone = document.getElementById('repairPhone').value;
            const bikeModel = document.getElementById('bikeModelRepair').value;
            const repairDate = document.getElementById('repairDate').value;
            const repairStatus = document.getElementById('repairStatus').value;
            const repairType = document.getElementById('repairType').value;
            const repairFee = document.getElementById('repairFee').value || 0;
            const repairNote = document.getElementById('repairNote').value;
            
            // 获取配件数据
            const partsUsed = [];
            const partsRows = document.querySelectorAll('#repairPartsTable tbody tr');
            partsRows.forEach(row => {
                const select = row.cells[0].querySelector('select');
                const id = parseInt(select.value);
                const quantity = parseInt(row.cells[1].querySelector('input').value);
                
                if (id && !isNaN(quantity)) {
                    partsUsed.push({
                        id,
                        quantity
                    });
                }
            });
            
            // 获取精品数据
            const premiumsUsed = [];
            const premiumsRows = document.querySelectorAll('#repairPremiumsTable tbody tr');
            premiumsRows.forEach(row => {
                const select = row.cells[0].querySelector('select');
                const id = parseInt(select.value);
                const quantity = parseInt(row.cells[1].querySelector('input').value);
                
                if (id && !isNaN(quantity)) {
                    premiumsUsed.push({
                        id,
                        quantity
                    });
                }
            });

            // 获取工时费数据
            let laborFee = 0;
            const laborRows = document.querySelectorAll('#repairLaborTable tbody tr');
            laborRows.forEach(row => {
                const quantity = parseInt(row.querySelector('.labor-quantity').value) || 0;
                const unitPrice = parseFloat(row.querySelector('.labor-unit-price').value) || 0;
                laborFee += quantity * unitPrice;
            });
            
            // 简单验证
            if (!customerName || !customerPhone || !bikeModel || !repairType || !repairDate) {
                showNotification('请填写必填字段', 'error');
                return;
            }
            
            if (editId) {
                // 更新现有维修记录
                const repairIndex = appData.repairs.findIndex(r => r.id === editId);
                if (repairIndex !== -1) {
                    appData.repairs[repairIndex] = {
                        ...appData.repairs[repairIndex],
                        customerName,
                        customerPhone,
                        bikeModel,
                        repairDate,
                        status: repairStatus,
                        repairType,
                        repairFee: parseFloat(repairFee),
                        partsUsed,
                        premiumsUsed,
                        laborFee,
                        note: repairNote
                    };
                    showNotification('维修记录更新成功！', 'success');
                }
            } else {
                // 创建新维修记录
                const newRepair = {
                    id: repairId,
                    customerName,
                    customerPhone,
                    bikeModel,
                    repairDate,
                    status: repairStatus,
                    repairType,
                    repairFee: parseFloat(repairFee),
                    partsUsed,
                    premiumsUsed,
                    laborFee,
                    note: repairNote
                };
                
                // 添加到数据
                appData.repairs.push(newRepair);
                appData.nextRepairId++;
                
                showNotification('维修记录保存成功！', 'success');
            }
            
            // 更新本地存储
            saveData();
            
            // 重新渲染表格
            renderRepairsTable();
            
            // 重置表单
            resetRepairForm();
        }

        // 保存新车库存
        function saveNewInventory() {
            const editId = document.getElementById('editNewInventoryId').value;
            const model = document.getElementById('newModel').value;
            const color = document.getElementById('newColor').value;
            const vin = document.getElementById('newVin').value;
            const stockDate = document.getElementById('newStockDate').value;
            const cost = document.getElementById('newCost').value;
            const price = document.getElementById('newPrice').value;
            const status = document.getElementById('newStatus').value;
            const vehicleAttribute = document.getElementById('vehicleAttribute').value;
            const note = document.getElementById('newNote').value;
            
            // 简单验证
            if (!model || !color || !vin || !cost || !price || !vehicleAttribute) {
                showNotification('请填写必填字段', 'error');
                return;
            }
            
            if (editId) {
                // 更新现有库存
                const itemIndex = appData.newInventory.findIndex(i => i.id == editId);
                if (itemIndex !== -1) {
                    appData.newInventory[itemIndex] = {
                        ...appData.newInventory[itemIndex],
                        model,
                        color,
                        vin,
                        stockDate,
                        cost: parseFloat(cost),
                        price: parseFloat(price),
                        status,
                        vehicleAttribute,
                        note
                    };
                    showNotification('车辆库存更新成功！', 'success');
                }
            } else {
                // 创建新库存
                const newItem = {
                    id: appData.nextNewInventoryId,
                    model,
                    color,
                    vin,
                    stockDate,
                    cost: parseFloat(cost),
                    price: parseFloat(price),
                    status,
                    vehicleAttribute,
                    note
                };
                
                // 添加到数据
                appData.newInventory.push(newItem);
                appData.nextNewInventoryId++;
                
                showNotification('车辆库存添加成功！', 'success');
            }
            
            // 更新本地存储
            saveData();
            
            // 重新渲染表格
            renderNewInventoryTable();
            updateInventorySummary();
            loadData(); // 更新维修表单中的配件下拉菜单
            
            // 重置表单
            resetNewInventoryForm();
        }

        // 保存二手车库存
        function saveUsedInventory() {
            const editId = document.getElementById('editUsedInventoryId').value;
            const model = document.getElementById('usedModel').value;
            const color = document.getElementById('usedColor').value;
            const vin = document.getElementById('usedVin').value;
            const stockDate = document.getElementById('usedStockDate').value;
            const mileage = document.getElementById('usedMileage').value;
            const cost = document.getElementById('usedCost').value;
            const price = document.getElementById('usedPrice').value;
            const status = document.getElementById('usedStatus').value;
            const note = document.getElementById('usedNote').value;
            
            // 简单验证
            if (!model || !color || !vin || !mileage || !cost || !price) {
                showNotification('请填写必填字段', 'error');
                return;
            }
            
            if (editId) {
                // 更新现有库存
                const itemIndex = appData.usedInventory.findIndex(i => i.id == editId);
                if (itemIndex !== -1) {
                    appData.usedInventory[itemIndex] = {
                        ...appData.usedInventory[itemIndex],
                        model,
                        color,
                        vin,
                        stockDate,
                        mileage: parseInt(mileage),
                        cost: parseFloat(cost),
                        price: parseFloat(price),
                        status,
                        note
                    };
                    showNotification('二手车库存更新成功！', 'success');
                }
            } else {
                // 创建新库存
                const newItem = {
                    id: appData.nextUsedInventoryId,
                    model,
                    color,
                    vin,
                    stockDate,
                    mileage: parseInt(mileage),
                    cost: parseFloat(cost),
                    price: parseFloat(price),
                    status,
                    note
                };
                
                // 添加到数据
                appData.usedInventory.push(newItem);
                appData.nextUsedInventoryId++;
                
                showNotification('二手车库存添加成功！', 'success');
            }
            
            // 更新本地存储
            saveData();
            
            // 重新渲染表格
            renderUsedInventoryTable();
            updateInventorySummary();
            
            // 重置表单
            resetUsedInventoryForm();
        }
        
        // 保存精品库存
function savePremiumInventory() {
    const editId = document.getElementById('editPremiumInventoryId').value;
    const name = document.getElementById('premiumName').value;
    const category = document.getElementById('premiumCategory').value;
    const model = document.getElementById('premiumModel').value;
    const quantity = document.getElementById('premiumQuantity').value;
    const cost = document.getElementById('premiumCost').value;
    const price = document.getElementById('premiumPrice').value;
    const stockDate = document.getElementById('premiumStockDate').value;
    const supplier = document.getElementById('premiumSupplier').value;
    const note = document.getElementById('premiumNote').value;
    
    // 简单验证
    if (!name || !category || !quantity || !cost || !price || !supplier) {
        showNotification('请填写必填字段', 'error');
        return;
    }
    
    if (editId) {
        // 更新现有库存
        const itemIndex = appData.premiumInventory.findIndex(i => i.id == editId);
        if (itemIndex !== -1) {
            appData.premiumInventory[itemIndex] = {
                ...appData.premiumInventory[itemIndex],
                name,
                category,
                model,
                quantity: parseInt(quantity),
                cost: parseFloat(cost),
                price: parseFloat(price),
                stockDate,
                supplier,
                note
            };
            showNotification('精品库存更新成功！', 'success');
        }
    } else {
        // 创建新库存
        const newItem = {
            id: appData.nextPremiumInventoryId,
            name,
            category,
            model,
            quantity: parseInt(quantity),
            cost: parseFloat(cost),
            price: parseFloat(price),
            stockDate,
            supplier,
            note
        };
        
        // 添加到数据
        appData.premiumInventory.push(newItem);
        appData.nextPremiumInventoryId++;
        
        showNotification('精品库存添加成功！', 'success');
    }
    
    // 更新本地存储
    saveData();
    
    // 重新渲染表格
    renderPremiumInventoryTable();
    updateInventorySummary();
    
    // 重置表单
    resetPremiumInventoryForm();
}

        // 保存配件库存
        function savePartInventory() {
            const editId = document.getElementById('editPartInventoryId').value;
            const name = document.getElementById('partName').value;
            const model = document.getElementById('partModel').value;
            const spec = document.getElementById('partSpec').value;
            const quantity = document.getElementById('partQuantity').value;
            const cost = document.getElementById('partCost').value;
            const price = document.getElementById('partPrice').value;
            const stockDate = document.getElementById('partStockDate').value;
            const supplier = document.getElementById('partSupplier').value;
            
            // 简单验证
            if (!name || !model || !spec || !quantity || !cost || !price || !supplier) {
                showNotification('请填写必填字段', 'error');
                return;
            }
            
            if (editId) {
                // 更新现有库存
                const itemIndex = appData.partsInventory.findIndex(i => i.id == editId);
                if (itemIndex !== -1) {
                    appData.partsInventory[itemIndex] = {
                        ...appData.partsInventory[itemIndex],
                        name,
                        model,
                        spec,
                        quantity: parseInt(quantity),
                        cost: parseFloat(cost),
                        price: parseFloat(price),
                        stockDate,
                        supplier
                    };
                    showNotification('配件库存更新成功！', 'success');
                }
            } else {
                // 创建新库存
                const newItem = {
                    id: appData.nextPartInventoryId,
                    name,
                    model,
                    spec,
                    quantity: parseInt(quantity),
                    cost: parseFloat(cost),
                    price: parseFloat(price),
                    stockDate,
                    supplier
                };
                
                // 添加到数据
                appData.partsInventory.push(newItem);
                appData.nextPartInventoryId++;
                
                showNotification('配件库存添加成功！', 'success');
            }
            
            // 更新本地存储
            saveData();
            
            // 重新渲染表格
            renderPartsInventoryTable();
            updateInventorySummary();
            loadData(); // 更新维修表单中的配件下拉菜单
            
            // 重置表单
            resetPartInventoryForm();
        }
        
        // 保存收款记录
        function savePayment() {
            const editId = document.getElementById('paymentId').value;
            const orderId = document.getElementById('paymentOrder').value;
            const paymentDate = document.getElementById('paymentDate').value;
            const paymentAmount = document.getElementById('paymentAmount').value;
            const paymentMethod = document.getElementById('paymentMethodFinance').value;
            const payer = document.getElementById('payer').value;
            const receiver = document.getElementById('receiverFinance').value;
            const note = document.getElementById('paymentNote').value;
            
            // 简单验证
            if (!orderId || !paymentAmount || !payer || !receiver) {
                showNotification('请填写必填字段', 'error');
                return;
            }
            
            if (editId && appData.payments.some(p => p.id === editId)) {
                // 编辑已有收款记录
                const paymentIndex = appData.payments.findIndex(p => p.id === editId);
                if (paymentIndex !== -1) {
                    appData.payments[paymentIndex] = {
                        id: editId,
                        orderId,
                        paymentDate,
                        amount: parseFloat(paymentAmount),
                        paymentMethod,
                        payer,
                        receiver,
                        note
                    };
                    showNotification('收款记录更新成功！', 'success');
                }
            } else {
                // 新增收款记录
                const newPayment = {
                    id: editId,
                    orderId,
                    paymentDate,
                    amount: parseFloat(paymentAmount),
                    paymentMethod,
                    payer,
                    receiver,
                    note
                };
                appData.payments.push(newPayment);
                appData.nextPaymentId++;
                showNotification('收款记录保存成功！', 'success');
            }
            
            // 更新本地存储
            saveData();
            
            // 重新渲染表格
            renderPaymentsTable();
            
            // 重置表单
            resetPaymentForm();
        }
        
        // 重置收款表单
        function resetPaymentForm() {
            document.getElementById('paymentId').value = generatePaymentId();
            document.getElementById('paymentOrder').value = '';
            document.getElementById('paymentAmount').value = '';
            document.getElementById('paymentMethodFinance').value = 'wechat';
            document.getElementById('payer').value = '';
            document.getElementById('receiverFinance').value = '';
            document.getElementById('paymentNote').value = '';
        }
        
        // 编辑收款记录
        function editPayment(id) {
            const payment = appData.payments.find(p => p.id === id);
            if (!payment) return;
            
            document.getElementById('paymentId').value = payment.id;
            document.getElementById('paymentOrder').value = payment.orderId;
            document.getElementById('paymentDate').value = payment.paymentDate;
            document.getElementById('paymentAmount').value = payment.amount;
            document.getElementById('paymentMethodFinance').value = payment.paymentMethod;
            document.getElementById('payer').value = payment.payer;
            document.getElementById('receiverFinance').value = payment.receiver;
            document.getElementById('paymentNote').value = payment.note || '';
        }
        
        // 保存车型
        function saveBikeModel() {
            const editId = document.getElementById('editBikeModelId').value;
            const name = document.getElementById('bikeModelName').value;
            // Removed bikeModelCategory and bikeModelPrice as they are deleted
            const note = document.getElementById('bikeModelNote').value;
            
            // 简单验证
            if (!name) {
                showNotification('请填写必填字段', 'error');
                return;
            }
            
            if (editId) {
                // 更新现有车型
                const modelIndex = appData.bikeModels.findIndex(m => m.id == editId);
                if (modelIndex !== -1) {
                    appData.bikeModels[modelIndex] = {
                        ...appData.bikeModels[modelIndex],
                        name,
                        // category and price removed
                        note
                    };
                    showNotification('车型更新成功！', 'success');
                }
            } else {
                // 创建新车型
                const newModel = {
                    id: appData.nextBikeModelId,
                    name,
                    // category and price removed
                    note
                };
                
                // 添加到数据
                appData.bikeModels.push(newModel);
                appData.nextBikeModelId++;
                
                showNotification('车型添加成功！', 'success');
            }
            
            // 更新本地存储
            saveData();
            
            // 重新渲染表格
            loadBikeModels();
            loadData(); // 更新下拉菜单
            
            // 重置表单
            resetBikeModelForm();
        }
        
            // 保存系统设置
            // function saveSettings() {
            //     const companyName = document.getElementById('companyName').value;
            //     const companyAddress = document.getElementById('companyAddress').value;
            //     const contactPhone = document.getElementById('contactPhone').value;
            //     const contactEmail = document.getElementById('contactEmail').value;
                
            //     appData.settings = {
            //         companyName,
            //         companyAddress,
            //         contactPhone,
            //         contactEmail
            //     };
                
            //     saveData();
            //     showNotification('系统设置保存成功！', 'success');
            // }

            // 删除项目
            function deleteItem(type, id) {
            currentEditId = id;
            
            // 设置删除回调
            deleteCallback = function() {
                let itemName = '';
                let canDelete = true;
                
                switch(type) {
                    case 'order':
                        // 检查是否有交车记录关联
                        if (appData.deliveries.some(d => d.orderId === id)) {
                            showNotification('该订单已有交车记录，不能删除！', 'error');
                            canDelete = false;
                        } else {
                            appData.orders = appData.orders.filter(item => item.id !== id);
                            itemName = '订单';
                        }
                        break;
                    case 'delivery':
                        // 删除交车记录时，恢复对应订单状态和车辆库存状态
                        const deliveryToDelete = appData.deliveries.find(item => item.id === id);
                        if (deliveryToDelete) {
                            // 恢复订单状态为期货或现货（这里假设恢复为期货futures）
                            const order = appData.orders.find(o => o.id === deliveryToDelete.orderId);
                            if (order) {
                                order.status = 'futures';
                            }
                            // 恢复车辆库存状态为在库
                            const inventoryItem = appData.newInventory.find(item => item.vin === deliveryToDelete.vin);
                            if (inventoryItem) {
                                inventoryItem.status = 'in_stock';
                            }
                        }
                        appData.deliveries = appData.deliveries.filter(item => item.id !== id);
                        itemName = '交车记录';
                        break;
                    case 'repair':
                        appData.repairs = appData.repairs.filter(item => item.id !== id);
                        itemName = '维修记录';
                        break;
                    case 'newInventory':
                        // 直接删除新车库存数据，不做订单关联检查
                        appData.newInventory = appData.newInventory.filter(item => item.id != id);
                        itemName = '新车库存';
                        break;
                    case 'usedInventory':
                        // 直接删除二手车库存数据，不做订单关联检查
                        // 清理无用关联检查代码
                        appData.usedInventory = appData.usedInventory.filter(item => item.id != id);
                        itemName = '二手车库存';
                        break;
                    case 'premiumInventory':
                        appData.premiumInventory = appData.premiumInventory.filter(item => item.id != id);
                        itemName = '精品库存';
                        break;
                    case 'partsInventory':
                        appData.partsInventory = appData.partsInventory.filter(item => item.id != id);
                        itemName = '配件库存';
                        break;
                    case 'payment':
                        appData.payments = appData.payments.filter(item => item.id !== id);
                        itemName = '收款记录';
                        break;
                    case 'bikeModel':
                        // 检查是否在库存中使用
                        if (appData.newInventory.some(i => i.model === id) || 
                            appData.orders.some(o => o.bikeModel === id)) {
                            showNotification('该车型已被使用，不能删除！', 'error');
                            canDelete = false;
                        } else {
                            appData.bikeModels = appData.bikeModels.filter(item => item.id != id);
                            itemName = '车型';
                        }
                        break;
                }
                
                if (canDelete) {
                    saveData();
                    renderAllTables();
                    updateInventorySummary();
                    showNotification(`${itemName}删除成功！`, 'success');
                }
                closeConfirmation();
            };
            
            document.getElementById('confirmationText').textContent = `确定要删除此${getItemName(type)}吗？`;
            document.getElementById('confirmationModal').classList.add('active');
        }
        
        // 获取项目名称
        function getItemName(type) {
            switch(type) {
                case 'order': return '订单';
                case 'delivery': return '交车记录';
                case 'repair': return '维修记录';
                case 'newInventory': return '新车库存';
                case 'usedInventory': return '二手车库存';
                case 'premiumInventory': return '精品库存';
                case 'partsInventory': return '配件库存';
                case 'payment': return '收款记录';
                case 'bikeModel': return '车型';
                default: return '项目';
            }
        }
        
        // 确认删除
        function confirmDelete() {
            if (deleteCallback) {
                deleteCallback();
            }
        }
        
        // 关闭确认对话框
        function closeConfirmation() {
            document.getElementById('confirmationModal').classList.remove('active');
            currentEditId = null;
            deleteCallback = null;
        }

function updateInventorySummary() {
    // 新车库存，改为根据车辆属性是新车且状态为在库统计
    const newInventoryCount = appData.newInventory.filter(item => item.status === 'in_stock' && item.vehicleAttribute === 'new').length;
    document.getElementById('newInventoryCount').textContent = newInventoryCount;
    
    // 二手车库存，改为统计车辆库存列表中所有车辆属性为二手车的数量总和
    const usedInventoryCount = appData.newInventory.filter(item => item.status === 'in_stock' && item.vehicleAttribute === 'used').length;
    document.getElementById('usedInventoryCount').textContent = usedInventoryCount;
    
    // 精品库存
    const premiumInventoryCount = appData.premiumInventory.reduce((sum, item) => sum + parseInt(item.quantity), 0);
    document.getElementById('premiumInventoryCount').textContent = premiumInventoryCount;
    
    // 配件库存
    const partsInventoryCount = appData.partsInventory.reduce((sum, item) => sum + parseInt(item.quantity), 0);
    document.getElementById('partsInventoryCount').textContent = partsInventoryCount;
    
    // 本月订单数
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    const monthlyOrderCount = appData.orders
        .filter(order => {
            const orderDate = new Date(order.orderDate);
            return orderDate.getMonth() + 1 === currentMonth &&
                   orderDate.getFullYear() === currentYear;
        }).length;
    document.getElementById('monthlyOrderCount').textContent = monthlyOrderCount;
    
    // 本月交车数
    const monthlyDeliveryCount = appData.deliveries
        .filter(delivery => {
            const deliveryDate = new Date(delivery.deliveryDate);
            return deliveryDate.getMonth() + 1 === currentMonth &&
                   deliveryDate.getFullYear() === currentYear;
        }).length;
    document.getElementById('monthlyDeliveryCount').textContent = monthlyDeliveryCount;
    
    // 本月销售额（交车状态的销售总额，统计口径调整）
    const monthlySales = appData.orders
        .filter(order => {
            const deliveryDate = new Date(order.deliveryDate);
            return deliveryDate.getMonth() + 1 === currentMonth && 
                   deliveryDate.getFullYear() === currentYear &&
                   order.status === 'delivery';
        })
        .reduce((sum, order) => sum + parseFloat(order.amount), 0);
    
    document.getElementById('monthlySales').textContent = '¥' + monthlySales.toFixed(2);
    
    // 本月维修额
    const monthlyRepairRevenue = appData.repairs
        .filter(repair => {
            const repairDate = new Date(repair.repairDate);
            return repairDate.getMonth() + 1 === currentMonth && 
                   repairDate.getFullYear() === currentYear;
        })
        .reduce((sum, repair) => sum + parseFloat(repair.repairFee || 0), 0);
    
    document.getElementById('monthlyRepairRevenue').textContent = '¥' + monthlyRepairRevenue.toFixed(2);
}

        // 初始化图表
        function initCharts() {
            // 库存车型分布图表
            const inventoryBikeModelCtx = document.getElementById('inventoryBikeModelChart').getContext('2d');
            window.inventoryBikeModelChart = new Chart(inventoryBikeModelCtx, {
                type: 'pie',
                data: getInventoryBikeModelDistributionData(),
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        datalabels: {
                            color: '#000',
                            formatter: function(value, context) {
                                return value;
                            },
                            font: {
                                weight: 'bold',
                                size: 14
                            }
                        }
                    }
                },
                plugins: [ChartDataLabels]
            });
            
            // 订单车型分布图表
            const bikeModelCtx = document.getElementById('bikeModelChart').getContext('2d');
            window.bikeModelChart = new Chart(bikeModelCtx, {
                type: 'pie',
                data: getBikeModelDistributionData(),
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        datalabels: {
                            color: '#000',
                            formatter: function(value, context) {
                                return value;
                            },
                            font: {
                                weight: 'bold',
                                size: 14
                            }
                        }
                    }
                },
                plugins: [ChartDataLabels]
            });
        }
        
        // 获取订单车型分布数据
        function getBikeModelDistributionData() {
            const modelCounts = {};
            
            appData.orders.forEach(order => {
            if (!modelCounts[order.bikeModel]) {
                modelCounts[order.bikeModel] = 0;
            }
                modelCounts[order.bikeModel] += parseInt(order.quantity);
            });
            
            const labels = Object.keys(modelCounts);
            const data = Object.values(modelCounts);
            
            // 生成颜色
            const backgroundColors = [];
            for (let i = 0; i < labels.length; i++) {
                backgroundColors.push(getRandomColor());
            }
            
            return {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors,
                    borderWidth: 1
                }]
            };
        }

        // 获取库存车型分布数据
        function getInventoryBikeModelDistributionData() {
            const modelCounts = {};
            
            // 统计新车库存
            appData.newInventory.forEach(item => {
                if (item.status === 'in_stock' || item.status === 'reserved') {
                    if (!modelCounts[item.model]) {
                        modelCounts[item.model] = 0;
                    }
                    modelCounts[item.model] += 1;
                }
            });
            
            // 统计二手车库存
            appData.usedInventory.forEach(item => {
                if (item.status === 'in_stock' || item.status === 'reserved') {
                    if (!modelCounts[item.model]) {
                        modelCounts[item.model] = 0;
                    }
                    modelCounts[item.model] += 1;
                }
            });
            
            const labels = Object.keys(modelCounts);
            const data = Object.values(modelCounts);
            
            // 生成颜色
            const backgroundColors = [];
            for (let i = 0; i < labels.length; i++) {
                backgroundColors.push(getRandomColor());
            }
            
            return {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors,
                    borderWidth: 1
                }]
            };
        }
        
        // 生成随机颜色
        function getRandomColor() {
            const letters = '0123456789ABCDEF';
            let color = '#';
            for (let i = 0; i < 6; i++) {
                color += letters[Math.floor(Math.random() * 16)];
            }
            return color;
        }

        // 获取最近6个月的月份名称
        function getLastSixMonths() {
            const months = [];
            const monthNames = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];
            const date = new Date();
            
            for (let i = 5; i >= 0; i--) {
                const d = new Date(date.getFullYear(), date.getMonth() - i, 1);
                months.push(`${d.getFullYear()}年${monthNames[d.getMonth()]}`);
            }
            
            return months;
        }

        // 获取最近6个月的销售数据
        function getMonthlySalesData() {
            const salesData = [0, 0, 0, 0, 0, 0];
            const date = new Date();
            
            appData.orders.forEach(order => {
                const orderDate = new Date(order.orderDate);
                const monthDiff = (date.getMonth() - orderDate.getMonth()) + 
                                 (12 * (date.getFullYear() - orderDate.getFullYear()));
                
                if (monthDiff >= 0 && monthDiff < 6) {
                    salesData[5 - monthDiff] += parseFloat(order.amount);
                }
            });
            
            return salesData;
        }

        // 更新图表
        function updateCharts() {
            if (window.inventoryBikeModelChart) {
                window.inventoryBikeModelChart.destroy();
            }
            
            const inventoryBikeModelCtx = document.getElementById('inventoryBikeModelChart').getContext('2d');
            window.inventoryBikeModelChart = new Chart(inventoryBikeModelCtx, {
                type: 'pie',
                data: getInventoryBikeModelDistributionData(),
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        }

    // 保存数据到localStorage
    function saveData() {
        try {
            localStorage.setItem('niu-app-data', JSON.stringify(appData));
            updateInventorySummary();
        } catch (e) {
            console.error('保存数据失败', e);
            showNotification('保存数据失败: ' + e.message, 'error');
        }
    }

        // 渲染库存金额统计表格
        function renderInventoryValueTable() {
            const tbody = document.querySelector('#inventoryValueTable tbody');
            tbody.innerHTML = '';

            // 计算新车库存金额，增加车辆属性过滤
            const newInventoryItems = appData.newInventory.filter(item => 
                (item.status === 'in_stock' || item.status === 'reserved') && item.vehicleAttribute === 'new'
            );
            const newInventoryCount = newInventoryItems.length;
            const newInventoryCost = newInventoryItems.reduce((sum, item) => sum + parseFloat(item.cost || 0), 0);
            const newInventoryPrice = newInventoryItems.reduce((sum, item) => sum + parseFloat(item.price || 0), 0);

            // 计算二手车库存金额，改为从newInventory数组中采集，增加车辆属性过滤
            const usedInventoryItems = appData.newInventory.filter(item => 
                (item.status === 'in_stock' || item.status === 'reserved') && item.vehicleAttribute === 'used'
            );
            const usedInventoryCount = usedInventoryItems.length;
            const usedInventoryCost = usedInventoryItems.reduce((sum, item) => sum + parseFloat(item.cost || 0), 0);
            const usedInventoryPrice = usedInventoryItems.reduce((sum, item) => sum + parseFloat(item.price || 0), 0);

        // 计算精品库存金额
        const premiumInventoryCount = appData.premiumInventory.reduce((sum, item) => sum + parseInt(item.quantity || 0), 0);
        const premiumInventoryCost = appData.premiumInventory.reduce((sum, item) => sum + parseFloat(item.cost || 0), 0);
        const premiumInventoryPrice = appData.premiumInventory.reduce((sum, item) => sum + parseFloat(item.price || 0), 0);

        // 计算配件库存金额
        const partsInventoryCount = appData.partsInventory.reduce((sum, item) => sum + parseInt(item.quantity || 0), 0);
        const partsInventoryCost = appData.partsInventory.reduce((sum, item) => sum + parseFloat(item.cost || 0), 0);
        const partsInventoryPrice = appData.partsInventory.reduce((sum, item) => sum + parseFloat(item.price || 0), 0);

        // 添加行数据
        const rows = [
            {
                type: '新车库存',
                count: newInventoryCount,
                cost: newInventoryCost,
                price: newInventoryPrice
            },
            {
                type: '二手车库存',
                count: usedInventoryCount,
                cost: usedInventoryCost,
                price: usedInventoryPrice
            },
            {
                type: '精品库存',
                count: premiumInventoryCount,
                cost: premiumInventoryCost,
                price: premiumInventoryPrice
            },
            {
                type: '配件库存',
                count: partsInventoryCount,
                cost: partsInventoryCost,
                price: partsInventoryPrice
            }
        ];

        rows.forEach(row => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td style="padding: 10px; border: 1px solid #ddd;">${row.type}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">${row.count}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">¥${row.cost.toFixed(2)}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">¥${row.price.toFixed(2)}</td>
            `;
            tbody.appendChild(tr);
        });
    }

        // 显示通知
        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            const messageEl = document.getElementById('notification-message');
            
            messageEl.textContent = message;
            notification.className = 'notification ' + type;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 打开弹窗
        function openModal() {
            document.getElementById('detailModal').classList.add('active');
        }
        
        // 关闭弹窗
        function closeModal() {
            document.getElementById('detailModal').classList.remove('active');
        }

        // 示例数据初始化
        function initSampleData() {
            // 添加示例车型
            appData.bikeModels.push({
                id: 1,
                name: '小牛UQi+',
                category: 'new',
                price: 4899,
                note: '都市智能电动车'
            });
            
            appData.bikeModels.push({
                id: 2,
                name: '小牛MQi2',
                category: 'new',
                price: 5299,
                note: '智能锂电电动车'
            });
            
            appData.bikeModels.push({
                id: 3,
                name: '小牛NQi GT',
                category: 'new',
                price: 9999,
                note: '高端性能电动车'
            });
            
            appData.bikeModels.push({
                id: 4,
                name: '锂电池48V20Ah',
                category: 'parts',
                price: 1599,
                note: '高性能锂电池'
            });
            
            // 添加示例订单（使用新格式）
            appData.orders.push({
                id: 'DD-23-0001',
                customerName: '张先生',
                customerPhone: '13800138000',
                bikeModel: '小牛UQi+',
                quantity: 1,
                color: '白色',
                orderDate: '2023-07-10',
                deliveryDate: '2023-07-15',
                status: 'delivery',
                amount: 4899.00,
                deposit: 1000.00,
                note: '客户要求白色'
            });
            
            appData.orders.push({
                id: 'DD-23-0002',
                customerName: '刘先生',
                customerPhone: '13900139000',
                bikeModel: '小牛NQi GT',
                quantity: 1,
                color: '黑色',
                orderDate: '2023-07-11',
                deliveryDate: '2023-07-18',
                status: 'futures',
                amount: 9999.00,
                deposit: 5000.00,
                note: '期货订单'
            });
            
            // 添加示例交车记录（使用新格式）
            appData.deliveries.push({
                id: 'JC-23-0001',
                orderId: 'DD-23-0001',
                customerName: '张先生',
                bikeModel: '小牛UQi+',
                deliveryDate: '2023-07-15',
                vin: 'N1U2S3D4F5G6H7J8K',
                deliveryPerson: '王经理',
                accessoryData: {
                    parts: [
                        { name: '后视镜', quantity: 2, price: 50, total: 100 }
                    ],
                    premiums: [
                        { name: '安全头盔', quantity: 1, price: 199, total: 199 }
                    ]
                },
                note: '正常交车'
            });
            
            // 添加示例维修记录（使用新格式）
            appData.repairs.push({
                id: 'WX-23-0001',
                customerName: '陈女士',
                customerPhone: '13700137000',
                bikeModel: '小牛MQi2',
                repairDate: '2023-07-05',
                status: 'completed',
                repairType: '电池问题',
                repairFee: 450.00,
                partsUsed: [{id: 1, quantity: 1}],
                premiumsUsed: [{id: 1, quantity: 1}],
                note: '电池续航不足，更换新电池'
            });
            
            // 添加示例新车库存
            appData.newInventory.push({
                id: 1,
                model: '小牛UQi+',
                color: '白色',
                vin: 'N1U2S3D4F5G6H7J8K',
                stockDate: '2023-06-15',
                cost: 3850.00,
                price: 4899.00,
                status: 'sold',
                note: '2023新款'
            });
            
            appData.newInventory.push({
                id: 2,
                model: '小牛MQi2',
                color: '灰色',
                vin: 'M2Q3W4E5R6T7Y8U9I',
                stockDate: '2023-07-01',
                cost: 4200.00,
                price: 5299.00,
                status: 'in_stock',
                note: '配备智能系统'
            });
            
            // 添加示例二手车库存
            appData.usedInventory.push({
                id: 1,
                model: '小牛UQi',
                color: '蓝色',
                vin: 'U1I2O3P4A5S6D7F8G',
                stockDate: '2023-06-20',
                mileage: 1250,
                cost: 2800.00,
                price: 3200.00,
                status: 'in_stock',
                note: '轻微使用痕迹'
            });
            
            // 添加示例精品库存
            appData.premiumInventory.push({
                id: 1,
                name: '小牛安全头盔',
                category: 'helmet',
                model: '全系通用',
                quantity: 25,
                cost: 120.00,
                price: 199.00,
                stockDate: '2023-07-01',
                supplier: '安全装备厂',
                note: '高级安全头盔'
            });
            
            appData.premiumInventory.push({
                id: 2,
                name: '小牛专用车锁',
                category: 'lock',
                model: '全系通用',
                quantity: 30,
                cost: 85.00,
                price: 150.00,
                stockDate: '2023-07-05',
                supplier: '安防设备公司',
                note: '加强防盗车锁'
            });
            
            // 添加示例配件库存
            appData.partsInventory.push({
                id: 1,
                name: '锂电池48V20Ah',
                model: 'UQi+/MQi2',
                spec: 'NCLT-4820',
                quantity: 25,
                cost: 1250.00,
                price: 1599.00,
                stockDate: '2023-06-10',
                supplier: '宁德时代'
            });
            
            appData.partsInventory.push({
                id: 2,
                name: '轮胎90/90-12',
                model: '全系通用',
                spec: 'TT-909012',
                quantity: 42,
                cost: 85.00,
                price: 120.00,
                stockDate: '2023-06-12',
                supplier: '正新轮胎'
            });
            
            // 添加示例收款记录
            appData.payments.push({
                id: 'SK-23-0001',
                orderId: 'order_DD-23-0001',
                paymentDate: '2023-07-10',
                amount: 1000.00,
                paymentMethod: 'wechat',
                payer: '张先生',
                receiver: '王经理',
                note: '订金'
            });
            
            appData.payments.push({
                id: 'SK-23-0002',
                orderId: 'repair_WX-23-0001',
                paymentDate: '2023-07-05',
                amount: 450.00,
                paymentMethod: 'cash',
                payer: '陈女士',
                receiver: '李技师',
                note: '维修费用'
            });
            
            // 更新ID计数器
            appData.nextOrderId = 3;
            appData.nextDeliveryId = 2;
            appData.nextRepairId = 2;
            appData.nextNewInventoryId = 3;
            appData.nextUsedInventoryId = 2;
            appData.nextPremiumInventoryId = 3;
            appData.nextPartInventoryId = 3;
            appData.nextPaymentId = 3;
            appData.nextBikeModelId = 5;
            
            saveData();
        }
        
// 渲染待处理订单表格
function renderPendingOrdersTable() {
    const tbody = document.querySelector('#pendingOrdersTable tbody');
    tbody.innerHTML = '';
    
    const pendingOrders = appData.orders.filter(order => 
        order.status === 'in_stock' || order.status === 'futures'
    );
    
    if (pendingOrders.length === 0) {
        tbody.innerHTML = `<tr><td colspan="9" class="empty-state"><i class="fas fa-check-circle"></i><p>暂无待处理订单</p></td></tr>`;
        return;
    }
    
    const fragment = document.createDocumentFragment();
    
    pendingOrders.forEach(order => {
        const row = document.createElement('tr');
        row.dataset.id = order.id;
        
        // 状态显示
        let statusText, statusColor;
        if (order.status === 'in_stock') {
            statusText = '现货';
            statusColor = '#2ecc71';
        } else if (order.status === 'futures') {
            statusText = '期货';
            statusColor = '#3498db';
        }
        
        // 计算剩余天数 = 交付日期 - 当前日期，显示数字，负数表示已过期
        const currentDate = new Date();
        const deliveryDate = new Date(order.deliveryDate);
        const timeDiff = deliveryDate - currentDate;
        const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
        let daysRemainingClass = '';
        if (daysDiff >= 1 && daysDiff <= 7) {
            daysRemainingClass = 'days-remaining red';
        } else if (daysDiff >= 8 && daysDiff <= 15) {
            daysRemainingClass = 'days-remaining yellow';
        } else if (daysDiff >= 16) {
            daysRemainingClass = 'days-remaining green';
        } else {
            daysRemainingClass = 'days-remaining negative';
        }
        // 显示数字，负数表示已过期
        const daysRemainingText = daysDiff;
        
        row.innerHTML = `
            <td>${order.id}</td>
            <td>${order.customerName}</td>
            <td>${order.bikeModel}</td>
            <td>${order.quantity}</td>
            <td>${order.orderDate}</td>
            <td>${order.deliveryDate || '-'}</td>
            <td><span style="color: ${statusColor};">${statusText}</span></td>
            <td><span class="${daysRemainingClass}">${daysRemainingText}</span></td>
            <td class="action-cell">
                <button class="action-btn" style="background-color: #3498db; color: white;" onclick="processOrder('${order.id}')">
                    处理
                </button>
            </td>
        `;
        
        fragment.appendChild(row);
    });
    
    tbody.appendChild(fragment);
}
        
        // 渲染销售报表
        function renderSalesReportTable() {
            const tbody = document.querySelector('#salesReportTable tbody');
            tbody.innerHTML = '';
            
            // 示例数据
            const reportData = [
                { month: '2023年7月', orders: 18, sales: 15, revenue: 128650, avgPrice: 8576.67, growth: 12.5 },
                { month: '2023年6月', orders: 22, sales: 19, revenue: 114350, avgPrice: 6018.42, growth: 8.3 },
                { month: '2023年5月', orders: 17, sales: 15, revenue: 105600, avgPrice: 7040.00, growth: -4.2 },
                { month: '2023年4月', orders: 20, sales: 17, revenue: 110250, avgPrice: 6485.29, growth: 15.7 }
            ];
            
            const fragment = document.createDocumentFragment();
            
            reportData.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row.month}</td>
                    <td>${row.orders}</td>
                    <td>${row.sales}</td>
                    <td>¥${row.revenue.toLocaleString()}</td>
                    <td>¥${row.avgPrice.toFixed(2)}</td>
                    <td><span style="color: ${row.growth > 0 ? '#2ecc71' : '#e74c3c'};">${row.growth > 0 ? '+' : ''}${row.growth}%</span></td>
                `;
                fragment.appendChild(tr);
            });
            
            tbody.appendChild(fragment);
        }
        
        // 处理订单
        function processOrder(id) {
            const order = appData.orders.find(o => o.id === id);
            if (order) {
                if (order.status === 'in_stock' || order.status === 'futures') {
                    // 自动跳转到交车管理
                    document.querySelector('[data-tab="deliveries"]').click();
                    document.getElementById('deliveryOrder').value = id;
                    showNotification(`请为订单 ${id} 填写交车信息`, 'success');
                }
            }
        }
        
        // 导出数据
        function exportData() {
            // 创建Excel工作簿
            const wb = XLSX.utils.book_new();
            
            // 添加订单数据
            const ordersWs = XLSX.utils.json_to_sheet(appData.orders);
            XLSX.utils.book_append_sheet(wb, ordersWs, "订单");
            
            // 添加交车数据
            const deliveriesWs = XLSX.utils.json_to_sheet(appData.deliveries);
            XLSX.utils.book_append_sheet(wb, deliveriesWs, "交车记录");
            
            // 添加维修数据
            const repairsWs = XLSX.utils.json_to_sheet(appData.repairs);
            XLSX.utils.book_append_sheet(wb, repairsWs, "维修记录");
            
            // 添加库存数据
            const newInventoryWs = XLSX.utils.json_to_sheet(appData.newInventory);
            XLSX.utils.book_append_sheet(wb, newInventoryWs, "新车库存");
            
            const usedInventoryWs = XLSX.utils.json_to_sheet(appData.usedInventory);
            XLSX.utils.book_append_sheet(wb, usedInventoryWs, "二手车库存");
            
            const premiumInventoryWs = XLSX.utils.json_to_sheet(appData.premiumInventory);
            XLSX.utils.book_append_sheet(wb, premiumInventoryWs, "精品库存");
            
            const partsInventoryWs = XLSX.utils.json_to_sheet(appData.partsInventory);
            XLSX.utils.book_append_sheet(wb, partsInventoryWs, "配件库存");
            
            // 添加收款数据
            const paymentsWs = XLSX.utils.json_to_sheet(appData.payments);
            XLSX.utils.book_append_sheet(wb, paymentsWs, "收款记录");
            
            // 添加车型数据
            const bikeModelsWs = XLSX.utils.json_to_sheet(appData.bikeModels);
            XLSX.utils.book_append_sheet(wb, bikeModelsWs, "车型");
            
            // 导出Excel文件
            XLSX.writeFile(wb, "小牛电动车系统数据.xlsx");
            showNotification('数据导出成功！', 'success');
        }
        
        // 导入数据
        function importData(e) {
            const file = e.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, {type: 'array'});
                
                // 清空现有数据
                appData = {...dataModel};
                
                // 导入订单数据
                if (workbook.Sheets["订单"]) {
                    const ordersData = XLSX.utils.sheet_to_json(workbook.Sheets["订单"]);
                    appData.orders = ordersData;
                }
                
                // 导入交车数据
                if (workbook.Sheets["交车记录"]) {
                    const deliveriesData = XLSX.utils.sheet_to_json(workbook.Sheets["交车记录"]);
                    appData.deliveries = deliveriesData;
                }
                
                // 导入维修数据
                if (workbook.Sheets["维修记录"]) {
                    const repairsData = XLSX.utils.sheet_to_json(workbook.Sheets["维修记录"]);
                    appData.repairs = repairsData;
                }
                
                // 导入库存数据
                if (workbook.Sheets["新车库存"]) {
                    const newInventoryData = XLSX.utils.sheet_to_json(workbook.Sheets["新车库存"]);
                    appData.newInventory = newInventoryData;
                }
                
                if (workbook.Sheets["二手车库存"]) {
                    const usedInventoryData = XLSX.utils.sheet_to_json(workbook.Sheets["二手车库存"]);
                    appData.usedInventory = usedInventoryData;
                }
                
                if (workbook.Sheets["精品库存"]) {
                    const premiumInventoryData = XLSX.utils.sheet_to_json(workbook.Sheets["精品库存"]);
                    appData.premiumInventory = premiumInventoryData;
                }
                
                if (workbook.Sheets["配件库存"]) {
            const partsInventoryData = XLSX.utils.sheet_to_json(workbook.Sheets["配件库存"]);
                    appData.partsInventory = partsInventoryData;
                }
                
                // 导入收款数据
                if (workbook.Sheets["收款记录"]) {
                    const paymentsData = XLSX.utils.sheet_to_json(workbook.Sheets["收款记录"]);
                    appData.payments = paymentsData;
                }
                
                // 导入车型数据
                if (workbook.Sheets["车型"]) {
                    const bikeModelsData = XLSX.utils.sheet_to_json(workbook.Sheets["车型"]);
                    appData.bikeModels = bikeModelsData;
                }
                
                // 重新计算ID计数器
                recalculateCounters();
                
                // 保存数据
                saveData();
                
                // 重新渲染UI
                renderAllTables();
                updateInventorySummary();
                loadData();
                
                showNotification('数据导入成功！', 'success');
            };
            reader.readAsArrayBuffer(file);
        }
        
        // 重新计算ID计数器
        function recalculateCounters() {
            // 订单
            appData.nextOrderId = appData.orders.length > 0 ? 
                Math.max(...appData.orders.map(o => parseInt(o.id.split('-')[2]))) + 1 : 1;
            
            // 交车记录
            appData.nextDeliveryId = appData.deliveries.length > 0 ? 
                Math.max(...appData.deliveries.map(d => parseInt(d.id.split('-')[2]))) + 1 : 1;
                
            // 维修记录
            appData.nextRepairId = appData.repairs.length > 0 ? 
                Math.max(...appData.repairs.map(r => parseInt(r.id.split('-')[2]))) + 1 : 1;
                
            // 其他计数器类似...
        }

        // 添加配件行
        function addPartRow(tableId, partId = '', quantity = 1) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <select class="accessory-select" onchange="fillPartPrice(this); updateLaborTotal();">
                        <option value="">选择配件</option>
                        ${appData.partsInventory.map(part => 
                            `<option value="${part.id}" data-price="${part.price}" ${part.id == partId ? 'selected' : ''}>
                                ${part.name} (${part.spec})
                            </option>`
                        ).join('')}
                    </select>
                </td>
                <td><input type="number" min="1" value="${quantity}" class="quantity-input" onchange="calculateRowTotal(this); updateLaborTotal();"></td>
                <td><input type="number" min="0" step="0.01" placeholder="单价" class="price-input" onchange="calculateRowTotal(this); updateLaborTotal();"></td>
                <td><input type="number" min="0" step="0.01" placeholder="总价" class="total-input" readonly></td>
                <td><button class="action-btn" style="background-color: #e74c3c; color: white;" onclick="deleteRow(this); updateLaborTotal();">删除</button></td>
            `;
            tbody.appendChild(row);
            
            // 如果提供了partId，自动填充价格
            if (partId) {
                const part = appData.partsInventory.find(p => p.id == partId);
                if (part) {
                    const priceInput = row.querySelector('.price-input');
                    priceInput.value = part.price;
                    calculateRowTotal(priceInput);
                    updateLaborTotal();
                }
            }
        }
        
        // 添加精品行
        function addPremiumRow(tableId, premiumId = '', quantity = 1) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <select class="accessory-select" onchange="fillPremiumPrice(this); updateLaborTotal();">
                        <option value="">选择精品</option>
                        ${appData.premiumInventory.map(premium => 
                            `<option value="${premium.id}" data-price="${premium.price}" ${premium.id == premiumId ? 'selected' : ''}>
                                ${premium.name}
                            </option>`
                        ).join('')}
                    </select>
                </td>
                <td><input type="number" min="1" value="${quantity}" class="quantity-input" onchange="calculateRowTotal(this); updateLaborTotal();"></td>
                <td><input type="number" min="0" step="0.01" placeholder="单价" class="price-input" onchange="calculateRowTotal(this); updateLaborTotal();"></td>
                <td><input type="number" min="0" step="0.01" placeholder="总价" class="total-input" readonly></td>
                <td><button class="action-btn" style="background-color: #e74c3c; color: white;" onclick="deleteRow(this); updateLaborTotal();">删除</button></td>
            `;
            tbody.appendChild(row);
            
            // 如果提供了premiumId，自动填充价格
            if (premiumId) {
                const premium = appData.premiumInventory.find(p => p.id == premiumId);
                if (premium) {
                    const priceInput = row.querySelector('.price-input');
                    priceInput.value = premium.price;
                    calculateRowTotal(priceInput);
                    updateLaborTotal();
                }
            }
        }
        
        // 填充配件价格
        function fillPartPrice(select) {
            const row = select.closest('tr');
            const priceInput = row.querySelector('.price-input');
            const selectedOption = select.options[select.selectedIndex];
            
            if (selectedOption.value) {
                priceInput.value = selectedOption.getAttribute('data-price');
                calculateRowTotal(select);
            }
        }
        
        // 填充精品价格
        function fillPremiumPrice(select) {
            const row = select.closest('tr');
            const priceInput = row.querySelector('.price-input');
            const selectedOption = select.options[select.selectedIndex];
            
            if (selectedOption.value) {
                priceInput.value = selectedOption.getAttribute('data-price');
                calculateRowTotal(select);
            }
        }
        
        // 计算行总计
        function calculateRowTotal(input) {
            const row = input.closest('tr');
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const total = quantity * price;
            
            row.querySelector('.total-input').value = total.toFixed(2);
            
            // 更新选装总计（如果是交车记录）
            if (row.closest('#deliveryPartsTable') || row.closest('#deliveryPremiumsTable')) {
                updateAccessoryTotal();
            }
        }
        
        // 更新选装总计
        function updateAccessoryTotal() {
            let total = 0;
            
            // 计算配件总计
            const partsRows = document.querySelectorAll('#deliveryPartsTable tbody tr');
            partsRows.forEach(row => {
                const rowTotal = parseFloat(row.querySelector('.total-input').value) || 0;
                total += rowTotal;
            });
            
            // 计算精品总计
            const premiumsRows = document.querySelectorAll('#deliveryPremiumsTable tbody tr');
            premiumsRows.forEach(row => {
                const rowTotal = parseFloat(row.querySelector('.total-input').value) || 0;
                total += rowTotal;
            });
            
            document.getElementById('accessoryTotal').value = total.toFixed(2);
        }
        
        // 删除行
        function deleteRow(button) {
            const row = button.closest('tr');
            row.remove();
            
            // 更新选装总计（如果是交车记录）
            if (row.closest('#deliveryPartsTable') || row.closest('#deliveryPremiumsTable')) {
                updateAccessoryTotal();
            }
        }
        // 添加工时费行
        function addLaborRow() {
            const tbody = document.querySelector('#repairLaborTable tbody');
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>工时费</td>
                <td><input type="number" class="labor-quantity" value="1" min="0" style="width: 60px;" onchange="updateLaborRowTotal(this); updateLaborTotal();"></td>
                <td><input type="number" class="labor-unit-price" value="20" readonly style="width: 60px;"></td>
                <td><input type="number" class="labor-total-price" value="20" readonly style="width: 80px;"></td>
                <td><button class="action-btn delete-btn" style="background-color: #e74c3c; color: white;" onclick="deleteLaborRow(this); updateLaborTotal();">删除</button></td>
            `;
            tbody.appendChild(row);
        }

        // 更新单个工时费行总价
        function updateLaborRowTotal(input) {
            const row = input.closest('tr');
            const quantity = parseInt(row.querySelector('.labor-quantity').value) || 0;
            const unitPrice = parseFloat(row.querySelector('.labor-unit-price').value) || 0;
            const totalPriceInput = row.querySelector('.labor-total-price');
            const total = quantity * unitPrice;
            totalPriceInput.value = total.toFixed(2);
        }
        
        // 计算工时费合计
        function updateLaborTotal() {
            const partsRows = document.querySelectorAll('#repairPartsTable tbody tr');
            const premiumsRows = document.querySelectorAll('#repairPremiumsTable tbody tr');
            const laborRows = document.querySelectorAll('#repairLaborTable tbody tr');
            let total = 0;

            // 计算配件总价
            partsRows.forEach(row => {
                const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
                const price = parseFloat(row.querySelector('.price-input').value) || 0;
                total += quantity * price;
            });

            // 计算精品总价
            premiumsRows.forEach(row => {
                const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
                const price = parseFloat(row.querySelector('.price-input').value) || 0;
                total += quantity * price;
            });

            // 计算工时费总价
            laborRows.forEach(row => {
                const totalPriceInput = row.querySelector('.labor-total-price');
                if (totalPriceInput) {
                    total += parseFloat(totalPriceInput.value) || 0;
                }
            });

            // 显示合计金额
            const laborTotalElement = document.getElementById('laborTotalAmount');
            if (laborTotalElement) {
                laborTotalElement.textContent = `¥${total.toFixed(2)}`;
            }
        }

        // 删除工时费行
        function deleteLaborRow(button) {
            const row = button.closest('tr');
            row.remove();
        }
    </script>

    <script>
        // 渲染交车毛利统计表格
        function renderDeliveryProfitTable() {
            const tbody = document.querySelector('#deliveryProfitTable tbody');
            tbody.innerHTML = '';

            const profitByMonth = {};
            const salesByMonth = {};

            appData.deliveries.forEach(delivery => {
                const date = new Date(delivery.deliveryDate);
                if (isNaN(date)) return;

                const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

                const order = appData.orders.find(o => o.id === delivery.orderId);
                const inventory = appData.newInventory.find(i => i.vin === delivery.vin);

                if (order && inventory) {
                    // 计算配件成本和精品成本
                    let partsCost = 0;
                    let premiumsCost = 0;

                    if (delivery.accessoryData) {
                        if (delivery.accessoryData.parts) {
                            delivery.accessoryData.parts.forEach(part => {
                                // 改用name匹配，避免名称不一致问题，忽略大小写和空格
                                const partInfo = appData.partsInventory.find(p => p.name.trim().toLowerCase() === part.name.trim().toLowerCase());
                                if (partInfo) {
                                    partsCost += partInfo.cost * part.quantity;
                                } else {
                                    console.log(`配件未匹配: ${part.name}`);
                                }
                            });
                        }
                        if (delivery.accessoryData.premiums) {
                            delivery.accessoryData.premiums.forEach(premium => {
                                // 同样改用name匹配，忽略大小写和空格
                                const premiumInfo = appData.premiumInventory.find(p => p.name.trim().toLowerCase() === premium.name.trim().toLowerCase());
                                if (premiumInfo) {
                                    premiumsCost += premiumInfo.cost * premium.quantity;
                                } else {
                                    console.log(`精品未匹配: ${premium.name}`);
                                }
                            });
                        }
                    }

                    const profit = order.amount - inventory.cost - partsCost - premiumsCost;

                    if (!profitByMonth[monthKey]) {
                        profitByMonth[monthKey] = 0;
                    }
                    profitByMonth[monthKey] += profit;

                    if (!salesByMonth[monthKey]) {
                        salesByMonth[monthKey] = 0;
                    }
                    salesByMonth[monthKey] += order.amount;
                }
            });

            const sortedMonths = Object.keys(profitByMonth).sort();

            sortedMonths.forEach(month => {
                const profit = profitByMonth[month];
                const sales = salesByMonth[month] || 0;
                const profitRate = sales !== 0 ? (profit / sales * 100).toFixed(2) + '%' : 'N/A';

                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td style="padding: 10px; border: 1px solid #ddd;">${month}</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">${profit.toFixed(2)}</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">${sales.toFixed(2)}</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">${profitRate}</td>
                `;
                tbody.appendChild(tr);
            });
        }

        // 渲染维修毛利统计表格
        function renderRepairProfitTable() {
            const tbody = document.querySelector('#repairProfitTable tbody');
            tbody.innerHTML = '';

            const profitByMonth = {};
            const repairAmountByMonth = {};

            appData.repairs.forEach(repair => {
                const date = new Date(repair.repairDate);
                if (isNaN(date)) return;

                const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

                let partsCost = 0;
                if (repair.partsUsed) {
                    repair.partsUsed.forEach(item => {
                        const part = appData.partsInventory.find(p => p.id == item.id);
                        if (part) {
                            partsCost += part.cost * item.quantity;
                        }
                    });
                }

                let premiumsCost = 0;
                if (repair.premiumsUsed) {
                    repair.premiumsUsed.forEach(item => {
                        const premium = appData.premiumInventory.find(p => p.id == item.id);
                        if (premium) {
                            premiumsCost += premium.cost * item.quantity;
                        }
                    });
                }

                const repairFee = repair.repairFee ? parseFloat(repair.repairFee) : 0;

                const profit = repairFee - partsCost - premiumsCost;

                if (!profitByMonth[monthKey]) {
                    profitByMonth[monthKey] = 0;
                }
                profitByMonth[monthKey] += profit;

                if (!repairAmountByMonth[monthKey]) {
                    repairAmountByMonth[monthKey] = 0;
                }
                repairAmountByMonth[monthKey] += repairFee;
            });

            const sortedMonths = Object.keys(profitByMonth).sort();

            sortedMonths.forEach(month => {
                const profit = profitByMonth[month];
                const repairAmount = repairAmountByMonth[month] || 0;
                const profitRate = repairAmount !== 0 ? (profit / repairAmount * 100).toFixed(2) + '%' : 'N/A';

                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td style="padding: 10px; border: 1px solid #ddd;">${month}</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">${profit.toFixed(2)}</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">${repairAmount.toFixed(2)}</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">${profitRate}</td>
                `;
                tbody.appendChild(tr);
            });
        }

        // 扩展tab切换事件，支持财务tab
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                if (!tab.dataset.subtab) {
                    const tabName = tab.dataset.tab;

                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));

                    tab.classList.add('active');
                    const targetContent = document.getElementById(tabName);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }

                    if (tabName === 'reports') {
                        updateCharts();
                    } else if (tabName === 'repairs') {
                        loadPartsData();
                    } else if (tabName === 'settings') {
                        loadBikeModels();
                    } else if (tabName === 'financial') {
        // 渲染财务表格
        renderDeliveryProfitTable();
        renderRepairProfitTable();
        renderInventoryValueTable();
                    }
                } else {
                    const subtabName = tab.dataset.subtab;
                    const parent = tab.parentElement;

                    parent.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.subtab-content').forEach(stc => stc.classList.remove('active'));

                    tab.classList.add('active');
                    const targetContent = document.getElementById(`${subtabName}-inventory`);
                    if (targetContent) {
                        targetContent.classList.add('active');
                        targetContent.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            });
        });
    </script>
</body>
</html>
